# 存在 1/0 的配置只能用来取反判断，因为 '0' 也会被当成 true 处理，后续最好使用 true/false
# 所有配置默认都是不开启的
FAST_DEV=1/0 # 是否开启 ts check
PROXY_API_HOST=https://go.t2.moego.dev # 代理的 API 地址
REDUX_TRACE=true/false # 是否开启 Redux 追踪
CLICK_TO_COMPONENT_EDITOR=cursor/vscode # 点击跳转组件编辑器
HTTPS=true/false # 是否开启 HTTPS/HTTP2
NO_LAZY=1/0 # 是否禁用懒加载 lazyCompilation

# Finance Kit
PROXY_FINANCE_KIT=true/false # 是否代理 finance kit 到本地
FINANCE_KIT_ROOT=../moego-finance-kit # finance kit 的根目录 (切换成你本地的路径)
