/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/**/*.html',
    './node_modules/@moego/ui/dist/**/*.{js,jsx,mjs}',
    './node_modules/@moego/reporting/**/*.{js,jsx,ts,tsx}',
    './node_modules/@moego/business-components/**/*.{js,jsx,mjs}',
    './node_modules/@moego/call-center/**/*.{js,jsx,ts,tsx}',
    './node_modules/@moego/fn-components/src/**/*.{js,jsx,ts,tsx}',
    './node_modules/@moego/workflow/**/*.{js,jsx,ts,tsx}',
  ],
  presets: [require('./node_modules/@moego/ui/dist/tailwind/tailwind.umd.cjs')],
  corePlugins: {
    preflight: true,
  },
  plugins: [require('tailwind-scrollbar-hide')],
  extend: {
    aria: {
      asc: 'sort="ascending"',
      desc: 'sort="descending"',
    },
    margin: ['last'],
  },
  prefix: 'moe-',
  theme: {
    extend: {
      screens: {
        // 分割从 1441 开始，1440 保持分割前样式
        'tablet-l': '1441px',
      },
    },
  },
};
