{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "files": {"ignore": [".vscode", "public/sentry"]}, "formatter": {"enabled": true, "ignore": [".cache/", "build/", "lib/", "node_modules/", "pnpm-lock.yaml", "coverage/", "scripts/debug.js", "./index.html"], "indentStyle": "space", "indentWidth": 2}, "organizeImports": {"enabled": false, "ignore": [".cache/", "build/", "lib/", "node_modules/", "pnpm-lock.yaml", "coverage/", "scripts/debug.js", "./index.html"]}, "linter": {"enabled": true, "ignore": [".cache/", "build/", "lib/", "node_modules/", "pnpm-lock.yaml", "coverage/", "scripts/debug.js", "./index.html"], "rules": {"recommended": true, "a11y": {"noAutofocus": "off", "noBlankTarget": "off", "noLabelWithoutControl": "off", "useSemanticElements": "off", "useFocusableInteractive": "off", "noRedundantAlt": "off", "noSvgWithoutTitle": "off", "useAltText": "off", "useButtonType": "off", "useIframeTitle": "off", "useKeyWithClickEvents": "off", "useKeyWithMouseEvents": "off", "useValidAnchor": "off"}, "complexity": {"noBannedTypes": "off", "noExtraBooleanCast": "off", "noForEach": "off", "noStaticOnlyClass": "off", "noThisInStatic": "off", "noUselessCatch": "off", "noUselessConstructor": "off", "noUselessEmptyExport": "off", "noUselessFragments": "off", "noUselessLabel": "off", "noUselessRename": "off", "noUselessSwitchCase": "off", "noUselessTernary": "off", "noUselessTypeConstraint": "off", "useArrowFunction": "off", "useFlatMap": "off", "useLiteralKeys": "off", "useOptionalChain": "off", "useRegexLiterals": "off"}, "correctness": {"noChildrenProp": "off", "noConstantCondition": "off", "noEmptyPattern": "off", "noInnerDeclarations": "off", "noInvalidUseBeforeDeclaration": "off", "noSelfAssign": "off", "noSwitchDeclarations": "off", "noUnnecessaryContinue": "off", "noUnsafeFinally": "off", "noUnsafeOptionalChaining": "off", "noUnusedLabels": "off", "noVoidTypeReturn": "off", "useExhaustiveDependencies": "off", "useJsxKeyInIterable": "off"}, "performance": {"noAccumulatingSpread": "off", "noDelete": "off"}, "security": {"noDangerouslySetInnerHtml": "off", "noDangerouslySetInnerHtmlWithChildren": "off"}, "style": {"noArguments": "off", "noCommaOperator": "off", "noInferrableTypes": "off", "noNonNullAssertion": "off", "noParameterAssign": "off", "noUnusedTemplateLiteral": "off", "noUselessElse": "off", "noVar": "off", "useConst": "off", "useDefaultParameterLast": "off", "useEnumInitializers": "off", "useExponentiationOperator": "off", "useExportType": "off", "useImportType": "off", "useNodejsImportProtocol": "off", "useNumberNamespace": "off", "useSelfClosingElements": "off", "useShorthandFunctionType": "off", "useSingleVarDeclarator": "off", "useTemplate": "off"}, "suspicious": {"noArrayIndexKey": "off", "noAssignInExpressions": "off", "noAsyncPromiseExecutor": "off", "noConfusingLabels": "off", "noConfusingVoidType": "off", "noConstEnum": "off", "noDoubleEquals": "off", "noEmptyInterface": "off", "noExplicitAny": "off", "noExportsInTest": "off", "noExtraNonNullAssertion": "off", "noGlobalIsFinite": "off", "noGlobalIsNan": "off", "noImplicitAnyLet": "off", "noPrototypeBuiltins": "off", "noRedeclare": "off", "noRedundantUseStrict": "off", "noSelfCompare": "off", "noShadowRestrictedNames": "off", "noSuspiciousSemicolonInJsx": "off", "noThenProperty": "off", "noUnsafeDeclarationMerging": "off", "useIsArray": "off"}}}, "javascript": {"formatter": {"arrowParentheses": "always", "attributePosition": "auto", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "double", "lineWidth": 120, "quoteProperties": "asNeeded", "quoteStyle": "single", "semicolons": "always", "trailingCommas": "all"}}}