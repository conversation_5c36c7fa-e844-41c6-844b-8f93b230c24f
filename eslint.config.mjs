import pluginMoego from '@moego/eslint-plugin-moego-fe';
import pluginQuery from '@tanstack/eslint-plugin-query';
import { globalIgnores } from 'eslint/config';

/** @type {import('eslint').Linter.Config[]} */
export default [
  globalIgnores(['*.config.*', '*.spec.ts', 'src/openApi/', 'src/assets/']),
  { files: ['src/**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  {
    plugins: {
      '@tanstack/query': pluginQuery,
      'moego-fe': pluginMoego,
    },
  },
  ...pluginQuery.configs['flat/recommended'],
  ...pluginMoego.configs.recommended.web,
  {
    settings: {
      react: {
        version: '17.0.2',
      },
    },
    rules: {
      'moego-fe/disable-index': [
        'error',
        {
          exclude: ['src/index.tsx'],
        },
      ],
      'moego-fe/disable-export-default': [
        'error',
        {
          exclude: ['*.d.ts'],
        },
      ],
    },
  },
];
