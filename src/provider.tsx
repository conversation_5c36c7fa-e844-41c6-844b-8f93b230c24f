import { Provider, type StoreEnhancer, createStore } from 'amos';
import { type Store } from '@sourcebug/amos';
import { type DevToolsOptions, withDevTools } from 'amos-devtools';
import React from 'react';
import ReactDOM from 'react-dom';
import { useAmosInitListener } from './utils/unstable/useSubribeSelector';
import { GrowthBookFeatureList } from './utils/growthBook/growthBook.config';
import { getFeatureIsOn, growthBook } from './utils/growthBook/growthBook';

export const isEnableAmosAutoBatch = () => growthBook.isOn(GrowthBookFeatureList.EnableAmosAutoBatch);

const batchUpdateEnhancer: StoreEnhancer = (store) => {
  (store as unknown as Store).batchedUpdates = ReactDOM.unstable_batchedUpdates;
  return store;
};

const REDUX_TRACE = process.env.REDUX_TRACE === 'true';
export const store = __DEV__
  ? createStore(
      void 0,
      withDevTools({
        name: 'MoeG<PERSON> - <PERSON>',
        ...(REDUX_TRACE
          ? {
              trace: true,
              traceLimit: 10,
            }
          : {}),
      } as DevToolsOptions),
      batchUpdateEnhancer,
    )
  : createStore(void 0, batchUpdateEnhancer);

getFeatureIsOn(GrowthBookFeatureList.EnableAmosAutoBatch).then((isOn) => {
  if (!Object.isFrozen(store)) {
    // @ts-expect-error 由于在 rsbuild 中做了全局替换，类型不正常是预期，无影响
    store.isAutoBatch = isOn;
  }
});

export function AmosStoreProvider(props: React.PropsWithChildren<{}>) {
  useAmosInitListener(store);

  return <Provider store={store}>{props.children}</Provider>;
}

AmosStoreProvider.store = store;

/**
 * 遇到：Uncaught Error: [Amos] you are using hooks without <Provider />. 就可以用这个包一下组件
 * 常见场景🎬：Modal.confirm({ content: <SomeAmosData /> }); 弹出来的部分里面有用到amos的数据
 */
export function wrapperAmosStore<T>(AnyComponent: React.ComponentType<T>) {
  return (props: React.PropsWithChildren<T>) => {
    return (
      <AmosStoreProvider>
        <AnyComponent {...props} />
      </AmosStoreProvider>
    );
  };
}
