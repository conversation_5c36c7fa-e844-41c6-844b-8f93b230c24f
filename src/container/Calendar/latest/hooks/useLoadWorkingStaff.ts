import { useDispatch } from 'amos';
import { getStaffsWorkingHour } from '../../../../store/calendarLatest/actions/private/calendar.actions';
import { getStaffListForCalendar } from '../../../../store/calendarLatest/actions/private/calendar.actions';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSelectedRangeDates } from './useSelectedRangeDates';

export function useReloadWorkingStaff() {
  const dispatch = useDispatch();
  const { getRangeDates } = useSelectedRangeDates();

  /**
   * 注意非 calendar 页面使用时，staffsForCalendarBox 可能会没有值，避免 syncLocalStorage 同步错误的数据到 localstorage
   */
  const reloadWorkingStaff = useLatestCallback(async (syncLocalStorage = true) => {
    const dateRanges = getRangeDates();
    const [staffs] = await Promise.all(
      dispatch([getStaffListForCalendar(dateRanges, syncLocalStorage), getStaffsWorkingHour(dateRanges)]),
    );
    return staffs;
  });

  return reloadWorkingStaff;
}
