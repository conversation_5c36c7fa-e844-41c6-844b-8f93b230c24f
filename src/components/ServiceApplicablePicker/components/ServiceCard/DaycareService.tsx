import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { renderCountableNounPlurals } from '../../../../utils/utils';
import { Condition } from '../../../Condition';
import { type PetServicePriceContentProps, type RenderService } from '../../../PetAndServicePicker/types/types';
import { useFeedingInstructions } from '../../hooks/useFeedingInstructions';
import { useFormatDaycareSchedule } from '../../hooks/useFormatServiceTime';
import { useMedicationInstructions } from '../../hooks/useMedicationInstructions';
import { TagServiceDuration } from '../TagService/TagServiceDuration';
import { TagServicePrice } from '../TagService/TagServicePrice';
import { BlockInfo } from './BlockInfo';
import { LodgeRoom } from './LodgeRoom';

export interface DaycareServiceProps extends PetServicePriceContentProps {
  service: RenderService;
  mainServiceItemType?: ServiceItemType;
}

export const DaycareService = memo<DaycareServiceProps>(function BoardingService(props) {
  const { service, mainServiceItemType, priceContent } = props;
  const [business] = useSelector(selectCurrentBusiness());
  const { startTime, endTime } = service;
  const serviceTime = service.serviceTime || (endTime && startTime && endTime - startTime) || 0;

  const fmtFeeding = useFeedingInstructions();
  const fmtMedication = useMedicationInstructions();
  const formatSchedule = useFormatDaycareSchedule(mainServiceItemType);
  const getDurationFmt = (durationMinutes: number) => {
    const hour = Math.floor(durationMinutes / 60);
    const minute = durationMinutes % 60;
    return (
      `${(hour && renderCountableNounPlurals(hour, 'hour') + ' ') || ''}` +
      `${(minute && renderCountableNounPlurals(minute, 'min')) || ''}`
    );
  };

  const tagServicePrice = (
    <TagServicePrice
      hiddenIcon
      className="moe-flex-shrink-0"
      price={business.formatAmount(service.servicePrice || 0)}
      overrideType={service.priceOverrideType}
    />
  );

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      <div className="moe-flex moe-items-center">
        <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-items-stretch moe-gap-y-[4px]">
          <div className="moe-flex moe-items-center">
            <Heading size="5">{service.serviceName}</Heading>
          </div>
          <Condition if={mainServiceItemType !== ServiceItemType.DAYCARE}>
            <Text variant="small" className="moe-text-secondary moe-mr-xs">
              {formatSchedule(service)}
            </Text>
          </Condition>
          <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-secondary">
            {priceContent ? priceContent(service.serviceId + '', tagServicePrice) : tagServicePrice}
            <TagServiceDuration
              hiddenIcon
              hour
              className="moe-flex-shrink-0"
              durationFmt={getDurationFmt(serviceTime)}
            />
          </div>
        </div>
        <LodgeRoom lodgingId={service.lodgingId} />
      </div>
      <BlockInfo label="Feeding instructions" instructions={fmtFeeding(service.feedings)} />
      <BlockInfo label="Medication instructions" instructions={fmtMedication(service.medications)} />
    </div>
  );
});
