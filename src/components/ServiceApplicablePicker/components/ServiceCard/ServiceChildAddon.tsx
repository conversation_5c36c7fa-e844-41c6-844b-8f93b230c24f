import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import React, { memo } from 'react';
import { type RenderService } from '../../../PetAndServicePicker/types/types';
import { PetAddOnItem } from './PetAddOnItem';

export interface ServiceChildAddonProps {
  list?: RenderService[];
  mainServiceItemType?: ServiceItemType;
}

export const ServiceChildAddon = memo<ServiceChildAddonProps>((props) => {
  const { list, mainServiceItemType } = props;

  return (
    <>
      {list?.map((s) => (
        <PetAddOnItem key={s.serviceId} service={s} mainServiceItemType={mainServiceItemType} />
      ))}
    </>
  );
});
