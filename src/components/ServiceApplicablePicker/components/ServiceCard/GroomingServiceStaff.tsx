import { useSelector } from 'amos';
import React, { memo } from 'react';
import { staffMapBox } from '../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { MultiStaffWithAvatar } from '../../../MultiStaffWithAvatar/MultiStaffWithAvatar';

interface GroomingServiceStaffProps {
  staffIds?: (number | string)[];
}

export const GroomingServiceStaff = memo<GroomingServiceStaffProps>(function GroomingServiceStaff(props) {
  const { staffIds } = props;
  const [staffMap] = useSelector(staffMapBox);

  // staffIds全是非法id，也不显示。例如 waitlist 就不需要显示staff
  if (!staffIds?.length || staffIds.every((id) => !isNormal(id))) {
    return null;
  }

  return (
    <MultiStaffWithAvatar size="24px" staffList={staffIds.map((id) => staffMap.mustGetItem(+id))} tip abbreviation />
  );
});
