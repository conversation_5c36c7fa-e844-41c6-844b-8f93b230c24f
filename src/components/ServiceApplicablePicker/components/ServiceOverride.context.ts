import { createContext, useContext } from 'react';

export interface ServiceApplicablePickerContextValue {
  isDisableStaffOverride: boolean;
}

export const ServiceApplicablePickerContext = createContext<ServiceApplicablePickerContextValue>({
  isDisableStaffOverride: false,
});

export const ServiceApplicablePickerProvider = ServiceApplicablePickerContext.Provider;

export const useServiceApplicablePickerContext = () => {
  return useContext(ServiceApplicablePickerContext);
};
