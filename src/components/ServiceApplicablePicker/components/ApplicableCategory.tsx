import { Markup } from '@moego/ui';
import React from 'react';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { ApplicableServiceItem } from './ApplicableServiceItem';

export interface ApplicableCategoryProps {
  itemType: 'Checkbox' | 'Radio' | 'AsButton';
  title: React.ReactNode;
  serviceIds: number[];
  disabled?: (id: number) => boolean | undefined;
  checked?: (id: number) => boolean | undefined;
  onToggleService?: (id: number, checked: boolean) => Promise<void> | void;
  petIds: string[];
}

export function ApplicableCategory(props: ApplicableCategoryProps) {
  const { title, serviceIds, itemType = 'Checkbox', disabled, checked, onToggleService, petIds } = props;

  const handleServiceClick = useSerialCallback(async (id: number, checked: boolean) => {
    await onToggleService?.(id, checked);
  });

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      <Markup variant="small" className="moe-text-secondary">
        {title || 'UNCATEGORIZED'}
      </Markup>
      <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
        {serviceIds.map((id) => (
          <ApplicableServiceItem
            key={id}
            petIds={petIds}
            id={id}
            onClick={handleServiceClick}
            checked={checked ? checked(id) : false}
            type={itemType}
            isDisabled={disabled ? disabled(id) : false}
          />
        ))}
      </div>
    </div>
  );
}
