import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Button, Text, cn } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { useHistory } from 'react-router';
import { ServicesNav } from '../../../container/settings/Settings/ServicesSetting/types';
import { PATH_SERVICE_SETTING, type ServiceSettingParams } from '../../../router/paths';
import { WithPermission } from '../../GuardRoute/WithPermission';

interface ApplicableServicesEmptyProps {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  className?: string;
}

export type ConfigMapType = Partial<Record<ServiceType, { label: string; buttonText: string; trigger: () => void }>>;

export type RouteMapType = Partial<Record<ServiceItemType, ServiceSettingParams['panel']>>;

export const ApplicableServicesEmpty = memo((props: ApplicableServicesEmptyProps) => {
  const { serviceType, serviceItemType, className } = props;
  const history = useHistory();

  const { label, buttonText, trigger } = useMemo(() => {
    const config: ConfigMapType = {
      [ServiceType.ADDON]: {
        label: 'No applicable add-on has been found based on your setting.',
        buttonText: 'Set up add-on',
        trigger: () => {
          history.push(PATH_SERVICE_SETTING.build({ panel: ServicesNav.Addons }));
        },
      },
      [ServiceType.SERVICE]: {
        label: 'No applicable service has been found based on your setting.',
        buttonText: 'Set up service',
        trigger: () => {
          history.push(PATH_SERVICE_SETTING.build({ panel: ServicesNav.Services, childPanel: `${serviceItemType}` }));
        },
      },
    };

    return config[serviceType] || { label: '', buttonText: '', trigger: () => {} };
  }, [serviceType, serviceItemType]);

  return (
    <div
      className={cn(
        'moe-h-full moe-flex moe-flex-col moe-gap-s moe-rounded-m moe-justify-center moe-items-center moe-bg-neutral-sunken-0',
        className,
      )}
    >
      <Text variant="small">{label}</Text>
      <WithPermission permissions={['viewSetting', 'accessServiceSettings']}>
        <Button size="s" variant="tertiary-legacy" onPress={trigger}>
          {buttonText}
        </Button>
      </WithPermission>
    </div>
  );
});
