import { Checkbox, Heading, Radio, cn } from '@moego/ui';
import classNames from 'classnames';
import React from 'react';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { matchApptServiceScene } from '../../../container/Appt/store/appt.options';
import { ApptServiceScene } from '../../../container/Appt/store/appt.types';
import { Condition } from '../../Condition';
import { Switch } from '../../SwitchCase';
import { useServiceInfo } from '../hooks/useServiceInfo';
import { useServiceApplicablePickerContext } from './ServiceOverride.context';
import { TagServiceDuration } from './TagService/TagServiceDuration';
import { TagServicePrice } from './TagService/TagServicePrice';
import { TagServiceUnit } from './TagService/TagServiceUnit';

export interface ApplicableServiceItemProps {
  type?: 'Checkbox' | 'Radio' | 'AsButton';
  id: number;
  checked?: boolean;
  isDisabled?: boolean;
  /** checked 为点击后是否选中 */
  onClick?: (id: number, checked: boolean) => void;
  petIds: string[];
}

export function ApplicableServiceItem(props: ApplicableServiceItemProps) {
  const { id, onClick, checked, type = 'Checkbox', isDisabled, petIds } = props;

  const { isDisableStaffOverride } = useServiceApplicablePickerContext();

  const {
    serviceName,
    formattedPrice,
    formattedDuration,
    formattedMaxDuration,
    priceOverrideType,
    durationOverrideType,
    service: { priceUnit, serviceItemType },
  } = useServiceInfo({ serviceId: id, isDisableStaffOverride, petIds: petIds.map(Number) });
  const isCheckbox = type === 'Checkbox';
  const isButton = type === 'AsButton';

  const showDuration =
    matchApptServiceScene(ApptServiceScene.ServicePickerDuration, { serviceItemType }) || formattedMaxDuration;

  const right = (
    <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-gap-y-[4px]">
      <Heading size="5">{serviceName}</Heading>
      <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-[14px] moe-text-[#666] moe-leading-[18px] moe-font-medium">
        <TagServicePrice petIds={petIds} price={formattedPrice} overrideType={priceOverrideType} />
        <TagServiceUnit priceUnit={priceUnit}>
          <Condition if={showDuration}>
            <TagServiceDuration
              petIds={petIds}
              duration={formattedMaxDuration || formattedDuration}
              overrideType={durationOverrideType}
            />
          </Condition>
        </TagServiceUnit>
      </div>
    </div>
  );
  return (
    <div
      data-testid={ApptTestIds.ApptPetServiceOptionBtn}
      className={classNames(
        'moe-flex moe-justify-between moe-items-center moe-gap-y-[5px] moe-px-[16px] moe-py-[12px] moe-border moe-border-solid moe-rounded-m moe-select-none',
        checked ? 'moe-border-brand moe-bg-brand-subtle' : 'moe-border-[#CDCDCD]',
        isButton ? 'moe-bg-white moe-border-divider' : '',
        isButton && !isDisabled ? 'hover:moe-bg-neutral-sunken-0' : '',
        isDisabled ? 'moe-opacity-50' : 'moe-cursor-pointer',
      )}
      onClick={() => {
        if (isDisabled) return;
        onClick?.(id, !checked);
      }}
    >
      <Switch shortCircuit>
        <Switch.Case if={isButton}>{right}</Switch.Case>
        <Switch.Case if={isCheckbox}>
          <Checkbox
            classNames={{
              // base/content能防止文字overflow
              base: cn('moe-min-w-0'),
              content: cn('moe-flex-1 moe-min-w-0'),
              wrapper: cn('moe-items-center moe-gap-xs'),
            }}
            isDisabled={isDisabled}
            isSelected={checked}
          >
            {right}
          </Checkbox>
        </Switch.Case>
        <Switch.Case else>
          <Radio
            isDisabled={isDisabled}
            classNames={{
              // base/content能防止文字overflow
              base: cn('moe-min-w-0 moe-items-center moe-gap-xs'),
              content: cn('moe-flex-1 moe-min-w-0'),
              wrapper: cn('moe-items-center moe-gap-xs'),
            }}
            value={String(id)}
          >
            {right}
          </Radio>
        </Switch.Case>
      </Switch>
    </div>
  );
}
