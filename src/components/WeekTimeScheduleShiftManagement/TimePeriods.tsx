import classNames from 'classnames';
import React from 'react';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { SelectTimePeriod } from './SelectTimePeriod';
import type { TimePeriod } from './types';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface TimePeriodsProps {
  value?: TimePeriod[];
  defaultValue?: TimePeriod[];
  isDisabled?: boolean;
  onChange?: (v: TimePeriod[]) => void;
  onAdd?: (v: TimePeriod[]) => void;
  /** 新增下一项的默认时间 */
  nextPeriodValue?: (v: TimePeriod[]) => TimePeriod | null;
  addable?: (v: TimePeriod[], i: number) => boolean;
  deletable?: (v: TimePeriod[], i: number) => boolean;
  maxTime?: number;
  minuteStep?: number;
  className?: string;
}

const TIME_PERIOD_GAP = 5; // Add 时，下一个 period 间隔 5min
const TIME_PERIOD_RANGE = 60; // period 范围默认为 1h

export function TimePeriods(props: TimePeriodsProps) {
  const {
    defaultValue = [{ startTime: 0, endTime: 60 }],
    isDisabled,
    maxTime = 1435,
    onAdd,
    nextPeriodValue,
    addable,
    deletable,
    minuteStep,
    className,
  } = props;
  const [values, setValues] = useControllableValue<TimePeriod[]>(props, { defaultValue });
  const allSize = values.length;
  const canAdd = values[allSize - 1]?.endTime < maxTime;

  // 业务场景下，有多行 time picker row
  // 对每一项都要满足：上一项结束时间 < 当前项开始时间 < 当前项结束时间
  // 边界条件：第一项和最后一项
  const checkTimePeriodDisabled = (v: number, index: number, pos: keyof TimePeriod) =>
    pos === 'startTime'
      ? v >= values[index].endTime || (index > 0 && v < values[index - 1].endTime)
      : v <= values[index].startTime || (index !== allSize - 1 && v > values[index + 1].startTime);

  const updateTimeRange = (v: TimePeriod, index: number) => {
    const newValue = values.slice();
    newValue[index] = { ...v };
    setValues(newValue);
  };
  const moreThanOne = allSize > 1;

  const handleAdd = useLatestCallback(() => {
    const last = values[values.length - 1];
    const nextStartTime = Math.min(last.endTime + TIME_PERIOD_GAP, maxTime);
    const nextEndTime = Math.min(nextStartTime + TIME_PERIOD_RANGE, maxTime);

    const newTimeRange = values.concat([
      nextPeriodValue?.(values) ?? {
        // 最后结束时间 +5分钟
        startTime: nextStartTime,
        // 最后结束时间 +1小时5分钟
        endTime: nextEndTime,
      },
    ]);
    setValues(newTimeRange);
    onAdd?.(newTimeRange);
  });

  return (
    <div className={classNames('moe-inline-flex !moe-flex-col !moe-gap-y-[8px]', className)}>
      {values.map((item, curIndex) => {
        const key = `${item.startTime}-${item.endTime}`;
        const isLast = curIndex === allSize - 1;

        return (
          <SelectTimePeriod
            disabled={isDisabled}
            minuteStep={minuteStep}
            key={key}
            value={item}
            onChange={(newItem) => updateTimeRange(newItem, curIndex)}
            startDisabled={(v) => checkTimePeriodDisabled(v, curIndex, 'startTime')}
            endDisabled={(v) => checkTimePeriodDisabled(v, curIndex, 'endTime')}
            deletable={!isDisabled && (deletable?.(values, curIndex) ?? moreThanOne)}
            addable={!isDisabled && (addable?.(values, curIndex) ?? (isLast && canAdd))}
            onDelete={() => {
              const newRanges = values.filter((_, index) => index !== curIndex);
              setValues(newRanges);
            }}
            onAdd={handleAdd}
          />
        );
      })}
    </div>
  );
}
