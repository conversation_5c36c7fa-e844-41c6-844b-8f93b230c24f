import { Checkbox, Text } from '@moego/ui';
import React, { type PropsWithChildren } from 'react';
import { type FullWeekDay, FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { CopyPaste } from './CopyPaste';
import { TimePeriods, type TimePeriodsProps } from './TimePeriods';
import type { TimePeriod } from './types';
import { StaffSettingTestIds } from '../../config/testIds/setting';

export interface WeekTimePeriodRowProps extends Pick<TimePeriodsProps, 'value' | 'onChange'> {
  day: number;
  renderEmpty?: (day: number) => React.ReactNode;
  onCopy?: (days: FullWeekDay[]) => void;
  onCheckChange?: (checked: boolean) => void;
  getDefaultTimePeriod?: () => TimePeriod[];
}

export function WeekTimePeriodRow(props: PropsWithChildren<WeekTimePeriodRowProps>) {
  const {
    day,
    renderEmpty,
    onCopy,
    getDefaultTimePeriod = () => [{ startTime: 540, endTime: 1140 }], // default = 09:00 ~ 19:00
    children,
    onCheckChange,
  } = props;
  const dayOfWeek = FullWeekDayList[day];
  const [controlledValue, setValue] = useControllableValue<TimePeriod[]>(props, { defaultValue: [] });
  const value = Array.isArray(controlledValue) ? controlledValue : [];
  const hasValue = value.length > 0;

  const empty = (
    <Text variant="regular-short" className="moe-text-tertiary moe-leading-[40px] moe-align-middle moe-h-8px-500">
      {renderEmpty?.(day) ?? `Not working on ${dayOfWeek}`}
    </Text>
  );

  return (
    <div className="!moe-flex !moe-py-[20px] !moe-gap-x-[50px] !moe-justify-between">
      <div className="!moe-flex moe-mr-[60px]">
        <div className="!moe-w-[152px] moe-h-8px-500 moe-flex moe-items-center moe-flex-shrink-0">
          <Checkbox
            isSelected={hasValue}
            onChange={(isSelected) => {
              setValue(isSelected ? getDefaultTimePeriod() : []);
              onCheckChange?.(isSelected);
            }}
          >
            {dayOfWeek}
          </Checkbox>
        </div>
        <div>
          {hasValue ? (
            <div className="moe-flex moe-relative moe-gap-x-[12px]">
              {children}
              <TimePeriods value={value} onChange={setValue} />
            </div>
          ) : (
            empty
          )}
        </div>
      </div>
      <CopyPaste
        data-testid={StaffSettingTestIds.TimePeriodCopyPaste}
        title="Copy to"
        day={dayOfWeek}
        onApply={(days: FullWeekDay[]) => onCopy?.(days)}
      />
    </div>
  );
}
