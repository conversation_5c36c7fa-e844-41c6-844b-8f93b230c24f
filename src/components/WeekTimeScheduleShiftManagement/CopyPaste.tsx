import { MinorCopyOutlined } from '@moego/icons-react';
import { IconButton, type IconButtonProps } from '@moego/ui';
import React from 'react';
import { type FullWeekDay } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useBool } from '../../utils/hooks/useBool';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { CopyPasteModal } from '../WeekTimeScheduleV2/CopyPasteModal';

interface CopyPasteProps extends IconButtonProps {
  disabled?: boolean;
  onApply?: (days: FullWeekDay[]) => any;
  day: FullWeekDay;
  title?: string;
}

export const CopyPaste = ({ onApply, day, title, disabled, ...rest }: CopyPasteProps) => {
  // const [days, setDays] = useState(FullWeekDayList);
  const visible = useBool(false);
  // const checkedAll = days.length === FullWeekDayList.length;
  const handleApply = useSerialCallback(async (days: FullWeekDay[]) => {
    await onApply?.(days);
    visible.close();
  });

  return (
    <>
      <IconButton
        icon={<MinorCopyOutlined />}
        className="moe-min-w-[40px]"
        color="transparent"
        size="l"
        onPress={visible.open}
        tooltip="Copy"
        {...rest}
      />
      <CopyPasteModal visible={visible.value} onClose={visible.close} day={day} onApply={handleApply} />
    </>
  );
  // return (
  //   <Popover>
  //     <Popover.Trigger>
  //       <IconButton icon={<MinorCopyOutlined />} color="transparent" size="large" />
  //     </Popover.Trigger>
  //     <Popover.Content
  //       title={title}
  //       confirmButtonProps={{
  //         isLoading: handleApply.isBusy(),
  //         isDisabled: disabled,
  //       }}
  //       onConfirm={handleApply}
  //       confirmText="Apply"
  //       showCancelButton={false}
  //       showCloseIcon={false}
  //     >
  //       <div>
  //         {dayOfWeeks.sundayFirst.map((d) => {
  //           const weekday = FullWeekDayList[d];
  //           const disabledCheckbox = day === weekday;

  //           return (
  //             <Checkbox
  //               key={weekday}
  //               isDisabled={disabledCheckbox}
  //               isSelected={days.includes(weekday)}
  //               onChange={() => {
  //                 setDays(() => {
  //                   const index = days.indexOf(weekday);
  //                   const nextDays = days.slice();
  //                   if (index === -1) {
  //                     nextDays.push(weekday);
  //                     return nextDays;
  //                   } else {
  //                     nextDays.splice(index, 1);
  //                     return nextDays;
  //                   }
  //                 });
  //               }}
  //               className="moe-mt-xxs"
  //             >
  //               {weekday}
  //             </Checkbox>
  //           );
  //         })}
  //         <Checkbox
  //           isSelected={checkedAll}
  //           onChange={(isSelected) => {
  //             setDays(() => (isSelected ? FullWeekDayList.slice() : [day]));
  //           }}
  //           className="moe-mt-xxs"
  //         >
  //           All days
  //         </Checkbox>
  //       </div>
  //     </Popover.Content>
  //   </Popover>
  // );
};
