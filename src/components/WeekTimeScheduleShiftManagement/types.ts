import { type FullWeekDay } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { type TimePeriodType } from '../../store/onlineBooking/settings/availabilityBD.types';

export interface TimePeriod {
  startTime: number;
  endTime: number;
  /** Evaluation only */
  petCapacity?: number;
  type?: TimePeriodType;
}

type UpperWeekTimeScheduleValue = { [key in FullWeekDay]: TimePeriod[] };

export type WeekTimeScheduleValue = {
  [Property in keyof UpperWeekTimeScheduleValue as Lowercase<Property>]: UpperWeekTimeScheduleValue[Property];
};
