import classNames from 'classnames';
import React from 'react';
import { dayOfWeeks } from '../../store/business/business.boxes';
import { type FullWeekDay, FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { WeekTimePeriodRow, type WeekTimePeriodRowProps } from './WeekTimePeriodRow';
import type { TimePeriod, WeekTimeScheduleValue } from './types';

export interface TimesProps {
  value?: TimePeriod[];
  defaultValue?: TimePeriod[];
  onChange?: (v: TimePeriod[]) => void;
  onAdd?: (v: TimePeriod[]) => void;
  maxTime?: number;
}

export interface ExtraCopyProps {
  days: FullWeekDay[];
  day: number;
}

export interface WeekTimeScheduleProps<T extends Partial<WeekTimeScheduleValue> | null = Partial<WeekTimeScheduleValue>>
  extends Pick<WeekTimePeriodRowProps, 'renderEmpty' | 'getDefaultTimePeriod'> {
  className?: string;
  value?: T;
  onChange?: (val: T) => void;
  onCheckChange?: (v: { day: number; checked: boolean }) => void;
  onCopy?: (v: ExtraCopyProps) => void;
  children?: (day: number) => React.ReactNode;
}

export function WeekTimeSchedule<T extends Partial<WeekTimeScheduleValue>>(props: WeekTimeScheduleProps<T>) {
  const { className, renderEmpty, getDefaultTimePeriod, children, onCheckChange, onCopy } = props;
  const [value, setValue] = useControllableValue<Partial<WeekTimeScheduleValue>>(props, { defaultValue: {} });

  return (
    <div className={classNames('!moe-flex !moe-flex-col', className)}>
      {dayOfWeeks.sundayFirst.map((day) => {
        const dayKey: Lowercase<FullWeekDay> = FullWeekDayList[day].toLowerCase() as Lowercase<FullWeekDay>;
        const times = value[dayKey] ?? [];

        return (
          <WeekTimePeriodRow
            key={day}
            day={day}
            value={times}
            onChange={(times) => {
              setValue((preObj) => ({ ...preObj, [dayKey]: times }));
            }}
            getDefaultTimePeriod={getDefaultTimePeriod}
            renderEmpty={renderEmpty}
            onCopy={async (days) => {
              const lowerDays = days.map((i) => i.toLowerCase() as Lowercase<FullWeekDay>);
              const newValues = lowerDays.reduce(
                (pre, day) => ({
                  ...pre,
                  [day]: [...times],
                }),
                {} as Partial<WeekTimeScheduleValue>,
              );
              setValue((pre) => ({ ...pre, ...newValues }));
              await onCopy?.({ day, days });
            }}
            onCheckChange={(v) => onCheckChange?.({ day, checked: v })}
          >
            {children?.(day)}
          </WeekTimePeriodRow>
        );
      })}
    </div>
  );
}
