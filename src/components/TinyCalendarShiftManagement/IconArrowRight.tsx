import classNames from 'classnames';
import React from 'react';
import ArrowRight from '../../assets/svg/icon-arrow-right.svg';
import { SvgIcon } from '../Icon/Icon';

export interface IconArrowRightProps {
  reverse?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

export function IconArrowRight(props: IconArrowRightProps) {
  const { reverse, onClick, disabled } = props;

  return (
    <div
      className={classNames(
        'moe-group !moe-w-[24px] !moe-h-[24px] !moe-rounded-[20px] !moe-flex !moe-items-center !moe-justify-center !moe-border !moe-border-solid !moe-border-[#E6E6E6]',
        disabled ? '!moe-cursor-default' : '!moe-cursor-pointer',
        reverse ? '!moe-rotate-180' : '',
      )}
      onClick={disabled ? undefined : onClick}
    >
      <SvgIcon
        size={6}
        src={ArrowRight}
        className={classNames(disabled ? '!moe-text-[#c8cbd2]' : '!moe-text-[#333]')}
      />
    </div>
  );
}
