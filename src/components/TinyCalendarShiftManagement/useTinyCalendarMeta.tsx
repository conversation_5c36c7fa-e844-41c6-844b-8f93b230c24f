import { type Dayjs } from 'dayjs';
import { range } from 'lodash';
import { useMemo } from 'react';

import { FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';

export function useTinyCalendarMeta(currentDay: Dayjs) {
  return useMemo(() => {
    const preMonth = currentDay.subtract(1, 'month');
    const nextMonth = currentDay.add(1, 'month');
    // 当月多少天
    const daysInMon = currentDay.daysInMonth();
    // 当月1号，星期几
    const dayOfWeekOn1st = currentDay.startOf('month').day();
    // 当月最后一天，星期几
    const dayOfWeekOnLast = currentDay.endOf('month').day();
    // 上个月有多少天
    const daysOfPrevMonth = currentDay.subtract(1, 'month').daysInMonth();
    // 计算日期面板
    const currentMonDay = range(1, daysInMon + 1).map((_, index) => currentDay.date(index + 1));
    // 上个月最后几天，在当月的面板中，比如29 30 接下来是本月的1号
    const preMonDay = range(dayOfWeekOn1st)
      .map((i, index) => daysOfPrevMonth - index)
      .reverse()
      .map((day) => preMonth.date(day));
    // 下个月的头开始几天，在当月面板中，比如 1 2 跟着本月的30 31之后
    const nextMonDay = range(FullWeekDayList.slice(dayOfWeekOnLast + 1).length).map((_, index) =>
      nextMonth.date(index + 1),
    );
    // 当月面板中的天
    const daysInPannel = preMonDay.concat(currentMonDay).concat(nextMonDay);
    return {
      daysInMon,
      dayOfWeekOn1st,
      dayOfWeekOnLast,
      daysOfPrevMonth,
      daysInPannel,
    };
  }, [currentDay]);
}
