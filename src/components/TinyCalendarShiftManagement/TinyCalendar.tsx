import { MinorChevronLeftOutlined, MinorChevronRightOutlined } from '@moego/icons-react';
import { Heading, IconButton, Typography } from '@moego/ui';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import dayjs, { type Dayjs } from 'dayjs';
import React, { useMemo } from 'react';
import { useSetState } from 'react-use';
import { FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useTinyCalendarMeta } from './useTinyCalendarMeta';

export interface TinyCalendarProps {
  /** 面板默认展示哪一天 */
  defaultDate?: Dayjs;
  value?: Dayjs[];
  onChange?: (v: Dayjs[]) => void;
  className?: string;
  disabledDays?: Dayjs[] | ((date: Dayjs) => boolean | string | undefined);
  disableSwitchMonth?: (date: Dayjs, dir: 'pre' | 'next') => boolean;
}

export function TinyCalendar(props: TinyCalendarProps) {
  const { className, disabledDays = [], defaultDate, disableSwitchMonth } = props;
  const [selectedDays, setSelectedDays] = useControllableValue<Dayjs[]>(props, {
    defaultValue: [],
    transformPropValue2LocalState: (d?: Dayjs[]) => d ?? [],
  });
  const today = useMemo(() => dayjs().startOf('date'), []);
  const [state, setState] = useSetState({
    currentDay: selectedDays?.[0] ?? defaultDate ?? today,
  });
  const { currentDay } = state;
  const goPrevMonth = useLatestCallback(() =>
    setState(({ currentDay }) => ({ currentDay: currentDay.subtract(1, 'month') })),
  );
  const goNextMonth = useLatestCallback(() =>
    setState(({ currentDay }) => ({ currentDay: currentDay.add(1, 'month') })),
  );
  const { daysInPannel } = useTinyCalendarMeta(currentDay);

  return (
    <div className={className}>
      <div className="!moe-flex !moe-items-center !moe-justify-between">
        <Typography.Heading size="5">{currentDay.format('MMMM YYYY')}</Typography.Heading>
        <div className="moe-flex moe-items-center moe-gap-x-xs moe-mr-s">
          <IconButton
            variant="secondary"
            size="s"
            isDisabled={disableSwitchMonth?.(currentDay, 'pre')}
            onPress={goPrevMonth}
            icon={<MinorChevronLeftOutlined />}
          />
          <IconButton
            variant="secondary"
            size="s"
            isDisabled={disableSwitchMonth?.(currentDay, 'next')}
            onPress={goNextMonth}
            icon={<MinorChevronRightOutlined />}
          />
        </div>
      </div>
      <div className="!moe-mt-[16px] !moe-grid !moe-grid-cols-7 !moe-gap-x-[10px] !moe-select-none">
        {FullWeekDayList.map((day) => {
          return (
            <div
              key={day}
              className="!moe-w-[36px] !moe-text-center !moe-font-medium !moe-text-[14px] !moe-text-[#999] !moe-leading-[18px]"
            >
              {day.slice(0, 1).toUpperCase()}
            </div>
          );
        })}
      </div>
      <div className="!moe-mt-[10px] !moe-grid !moe-grid-cols-7 !moe-gap-x-[10px] !moe-gap-y-[6px] !moe-select-none">
        {daysInPannel.map((day, index) => {
          const isFuture = day.isSameOrAfter(today, 'date');
          const isToday = day.isSame(today, 'date');
          const isInCurrentMonth = day.isSame(currentDay, 'month');
          const preOrNextMonth = !isInCurrentMonth;
          const disabledInfo =
            typeof disabledDays === 'function'
              ? disabledDays(day)
              : disabledDays.some((date) => date.isSame(day, 'date'));
          const disabledMsg = typeof disabledInfo === 'string' && disabledInfo.length > 0 ? disabledInfo : null;
          const disabled = typeof disabledInfo === 'string' ? disabledInfo.length > 0 : disabledInfo;
          const isSelected = selectedDays.some((date) => date.isSame(day, 'date'));

          return (
            <Tooltip key={day.unix()} title={disabled ? (disabledMsg ?? 'Closed') : null}>
              <div
                className={classNames(
                  // box
                  'moe-w-[36px] moe-h-[36px] moe-flex moe-items-center moe-justify-center moe-rounded-[36px] moe-group',
                  preOrNextMonth
                    ? 'moe-cursor-default'
                    : disabled
                      ? 'moe-cursor-not-allowed'
                      : isFuture
                        ? 'moe-cursor-pointer'
                        : 'moe-cursor-not-allowed',
                  preOrNextMonth
                    ? 'moe-bg-white'
                    : isSelected
                      ? 'moe-bg-brand-bold'
                      : disabled
                        ? 'moe-bg-neutral-sunken-0'
                        : isFuture
                          ? 'moe-bg-[#FFF7F0] hover:moe-bg-brand-mild'
                          : '',
                  isSelected
                    ? 'moe-text-white'
                    : disabled
                      ? 'moe-text-tertiary'
                      : isFuture
                        ? 'moe-text-brand hover:moe-text-[var(--moe-color-bg-brand-bold-hover)]'
                        : 'moe-text-tertiary',
                )}
                onClick={() => {
                  const valid = isInCurrentMonth && isFuture && !disabled;
                  if (!valid) {
                    return;
                  }
                  if (isSelected) {
                    setSelectedDays((pre) => pre.filter((i) => !i.isSame(day, 'date')));
                  } else {
                    setSelectedDays((pre) => pre.concat(day));
                  }
                }}
              >
                {isInCurrentMonth ? (
                  <div className="!moe-relative">
                    <Heading size="6" className="moe-text-[currentColor]">
                      {day.format('D')}
                    </Heading>
                    {isToday && (
                      <div
                        className={classNames(
                          '!moe-absolute !moe-ml-[calc(50%-2px)] !moe-w-[4px] !moe-h-[4px] !moe-rounded-[4px]',
                          isSelected ? '!moe-bg-[#fff]' : 'moe-bg-brand-bold group-hover:moe-bg-brand-bold-hover',
                        )}
                      />
                    )}
                  </div>
                ) : (
                  <div />
                )}
              </div>
            </Tooltip>
          );
        })}
      </div>
    </div>
  );
}
