import { cn } from '@moego/ui';
import React, { type FC, useLayoutEffect } from 'react';

/**
 * 背景：部分布局依赖 flex-grow 动态拉伸的高度做滚动，但需要一个稳定高度的 box 来承载滚动内容，否则纯 CSS 滚动条无法生成
 * 实现：外层参与外部弹性高度布局，获得的宽高传给 absolute 定位的 box，实现效果
 */
export const FlexGrowFixedHeightBox: FC<{
  className?: string;
  innerClassName?: string;
}> = ({ className, innerClassName, children }) => {
  const detectorRef = React.useRef<HTMLDivElement>(null);
  const [height, setHeight] = React.useState<number | undefined>(undefined);
  const [width, setWidth] = React.useState<number | undefined>(undefined);

  useLayoutEffect(() => {
    if (detectorRef.current) {
      const detectorElement = detectorRef.current;
      setHeight(detectorElement.clientHeight);
      const observer = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setHeight(entry.target.clientHeight);
          setWidth(entry.target.clientWidth);
        }
      });
      observer.observe(detectorElement);
      return () => {
        observer.unobserve(detectorElement);
        observer.disconnect();
      };
    }
    return undefined;
  }, []);

  const content = height ? (
    <div
      className={cn('moe-absolute moe-top-0 moe-left-0 moe-overflow-hidden', innerClassName)}
      style={{ height, width }}
    >
      {children}
    </div>
  ) : null;
  return (
    <div ref={detectorRef} className={cn('moe-flex-1 moe-relative', className)}>
      {content}
    </div>
  );
};
