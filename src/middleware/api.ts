import { HttpClient, ResponseErrorHandlerPlugin } from '@moego/http-client';
import { MOE_ENV } from '../config/host/const';
import { type OpenApiModels } from '../openApi/schema';

const apiHost = 'https://nlb-api.moego.pet';

export const http =
  MOE_ENV === 'production'
    ? new HttpClient<OpenApiModels>({
        restfulBaseUrl: `${apiHost}/api`,
        rpcBaseUrl: apiHost,
        withCredentials: true,
      })
    : new HttpClient<OpenApiModels>();

export const resErrorHandler = new ResponseErrorHandlerPlugin();
