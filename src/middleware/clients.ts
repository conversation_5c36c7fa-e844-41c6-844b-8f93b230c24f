/*
 * @since 2023-10-09 15:09:45
 * <AUTHOR> <<EMAIL>>
 */

import { createHttpClient } from '@moego/api-web/createHttpClient';
import { type AccountAccessService } from '@moego/api-web/moego/api/account/v1/account_access_api';
import { type AccountInfoService } from '@moego/api-web/moego/api/account/v1/account_info_api';
import { type AccountingService } from '@moego/api-web/moego/api/accounting/v1/accounting_api';
import { type ActivityLogService } from '@moego/api-web/moego/api/activity_log/v1/activity_log_api';
import { type AgreementService } from '@moego/api-web/moego/api/agreement/v1/agreement_api';
import { type BusinessConversationService } from '@moego/api-web/moego/api/ai_assistant/v1/business_conversation_api';
import { type AppointmentService } from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type AppointmentCheckerService } from '@moego/api-web/moego/api/appointment/v1/appointment_checker_api';
import { type AppointmentScheduleService } from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import { type AppointmentTaskService } from '@moego/api-web/moego/api/appointment/v1/appointment_task_api';
import { type CalendarService } from '@moego/api-web/moego/api/appointment/v1/calendar_api';
import { type CheckInOutAlertService } from '@moego/api-web/moego/api/appointment/v1/check_in_out_alert_api';
import { type DailyReportService } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { type LodgingService } from '@moego/api-web/moego/api/appointment/v1/lodging_api';
import { type OverviewService } from '@moego/api-web/moego/api/appointment/v1/overview_api';
import { type PetDetailService } from '@moego/api-web/moego/api/appointment/v1/pet_detail_api';
import { type PetPlaygroupService } from '@moego/api-web/moego/api/appointment/v1/pet_playgroup_api';
import { type PrintCardService } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { type AutoMessageService } from '@moego/api-web/moego/api/auto_message/v1/auto_message_api';
import { type WorkflowService } from '@moego/api-web/moego/api/automation/v1/workflow_api';
import { type BrandedAppService } from '@moego/api-web/moego/api/branded_app/v1/branded_app_api';
import { type BusinessCustomerService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_api';
import { type BusinessCustomerPreferredFrequencyService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_preferred_frequency_api';
import { type BusinessCustomerReferralSourceService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_referral_source_api';
import { type BusinessCustomerRetentionService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_retention_api';
import { type BusinessCustomerSettingService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_setting_api';
import { type BusinessCustomerTagService } from '@moego/api-web/moego/api/business_customer/v1/business_customer_tag_api';
import { type BusinessPetBehaviorService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_behavior_api';
import { type BusinessPetBreedService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_breed_api';
import { type BusinessPetCoatTypeService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_coat_type_api';
import { type BusinessPetCodeService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_code_api';
import { type BusinessPetColorService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_color_api';
import { type BusinessPetEvaluationService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_evaluation_api';
import { type BusinessPetFeedingScheduleService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_feeding_schedule_api';
import { type BusinessPetFixedService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_fixed_api';
import { type BusinessPetIncidentReportService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_incident_report_api';
import { type BusinessPetIncidentTypeService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_incident_type_api';
import { type BusinessPetMedicationScheduleService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_medication_schedule_api';
import { type BusinessPetMetadataService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_metadata_api';
import { type BusinessPetNoteService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_note_api';
import { type BusinessPetSizeService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_size_api';
import { type BusinessPetTypeService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_type_api';
import { type BusinessPetVaccineService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_vaccine_api';
import { type BusinessPetVaccineRequestService } from '@moego/api-web/moego/api/business_customer/v1/business_pet_vaccine_request_api';
import { type LoanService } from '@moego/api-web/moego/api/capital/v1/loan_api';
import { type CustomerPackageService } from '@moego/api-web/moego/api/customer/v1/customer_package_api';
import { type PackageService } from '@moego/api-web/moego/api/promotion/v1/package_api';
import { type CallingService } from '@moego/api-web/moego/api/engagement/v1/calling_api';
import { type SettingService } from '@moego/api-web/moego/api/engagement/v1/setting_api';
import { type FileService } from '@moego/api-web/moego/api/file/v2/file_api';
import { type TranscodeService } from '@moego/api-web/moego/api/file/v2/transcode_api';
import { type CashDrawerService } from '@moego/api-web/moego/api/finance_tools/v1/cash_drawer_api';
import { type FulfillmentService } from '@moego/api-web/moego/api/fulfillment/v1/fulfillment_api';
import { type GoogleReserveIntegrationService } from '@moego/api-web/moego/api/google_partner/v1/google_reserve_integration_api';
import { type GroomingReportService } from '@moego/api-web/moego/api/grooming/v1/grooming_report_api';
import { type MapService } from '@moego/api-web/moego/api/map/v1/map_api';
import { type DiscountCodeService } from '@moego/api-web/moego/api/marketing/v1/discount_code_api';
import { type MembershipService } from '@moego/api-web/moego/api/membership/v1/membership_api';
import { type SubscriptionService } from '@moego/api-web/moego/api/membership/v1/subscription_api';
import { type AutoMessageTemplateService } from '@moego/api-web/moego/api/message/v1/auto_message_template_api';
import { type MarketingEmailService } from '@moego/api-web/moego/api/message/v1/marketing_email_service';
import { type MessageTemplateService } from '@moego/api-web/moego/api/message/v1/message_template_api';
import { type ScheduleMessageService } from '@moego/api-web/moego/api/message/v1/schedule_message_api';
import { type MetadataApiService } from '@moego/api-web/moego/api/metadata/v1/metadata_api';
import { type NotificationService } from '@moego/api-web/moego/api/notification/v1/notification_api';
import { type EvaluationService } from '@moego/api-web/moego/api/offering/v1/evaluation_api';
import { type GroupClassService } from '@moego/api-web/moego/api/offering/v1/group_class_api';
import { type LodgingTypeService } from '@moego/api-web/moego/api/offering/v1/lodging_type_api';
import { type LodgingUnitService } from '@moego/api-web/moego/api/offering/v1/lodging_unit_api';
import { type PetService } from '@moego/api-web/moego/api/offering/v1/pet_api';
import { type PlaygroupService } from '@moego/api-web/moego/api/offering/v1/playgroup_api';
import { type PricingRuleService } from '@moego/api-web/moego/api/offering/v1/pricing_rule_api';
import { type ServiceManagementService } from '@moego/api-web/moego/api/offering/v1/service_api';
import { type ServiceStaffService } from '@moego/api-web/moego/api/offering/v1/service_staff_api';
import { type ServiceStaffOverrideRuleService } from '@moego/api-web/moego/api/offering/v1/service_staff_override_rule';
import { type PricingRuleService as PricingRuleServiceV2 } from '@moego/api-web/moego/api/offering/v2/pricing_rule_api';
import { type AutomationService } from '@moego/api-web/moego/api/online_booking/v1/automation_api';
import { type BookingRequestService } from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { type CustomerAvailabilityService } from '@moego/api-web/moego/api/online_booking/v1/customer_availability_api';
import { type OBAvailabilitySettingService } from '@moego/api-web/moego/api/online_booking/v1/ob_availability_setting_api';
import { type OBStaffAvailabilityService } from '@moego/api-web/moego/api/online_booking/v1/ob_staff_availability_api';
import { type WaitlistService } from '@moego/api-web/moego/api/online_booking/v1/waitlist_api';
import { type AssignItemAmountApiService } from '@moego/api-web/moego/api/order/v1/assign_item_amount_api';
import { type OrderService } from '@moego/api-web/moego/api/order/v1/order_api';
import { type OrderDiscountCodeService } from '@moego/api-web/moego/api/order/v1/order_discount_code_api';
import { type ServiceChargeService } from '@moego/api-web/moego/api/order/v1/service_charge_api';
import { type ServiceChargeCompanyService } from '@moego/api-web/moego/api/order/v1/service_charge_company_api';
import { type SplitTipsService } from '@moego/api-web/moego/api/order/v1/split_tips_api';
import { type OrderService as OrderServiceV2 } from '@moego/api-web/moego/api/order/v2/order_api';
import { type BusinessService } from '@moego/api-web/moego/api/organization/v1/business_api';
import { type CameraService } from '@moego/api-web/moego/api/organization/v1/camera_api';
import { type CompanyService } from '@moego/api-web/moego/api/organization/v1/company_api';
import { type StaffService } from '@moego/api-web/moego/api/organization/v1/staff_api';
import { type StaffTrackingService } from '@moego/api-web/moego/api/organization/v1/staff_tracking_api';
import { type PaymentService } from '@moego/api-web/moego/api/payment/v2/payment_api';
import { type PaymentOnboardService } from '@moego/api-web/moego/api/payment/v2/payment_onboard_api';
import { type PermissionService } from '@moego/api-web/moego/api/permission/v1/permission_api';
import { type PromotionService } from '@moego/api-web/moego/api/promotion/v1/prompt_api';
import { type AttributeService } from '@moego/api-web/moego/api/reporting/v2/attribute_api';
import { type CustomReportService } from '@moego/api-web/moego/api/reporting/v2/custom_report_api';
import { type DashboardService } from '@moego/api-web/moego/api/reporting/v2/dashboard_api';
import { type ReportService } from '@moego/api-web/moego/api/reporting/v2/reports_api';
import { type SmartScheduleSettingService } from '@moego/api-web/moego/api/smart_scheduler/v1/smart_schedule_setting_api';
import { type TimeSlotService } from '@moego/api-web/moego/api/smart_scheduler/v1/time_slot_api';
import { type SubscriptionService as CommonSubscriptionService } from '@moego/api-web/moego/api/subscription/v1/subscription_api';
import { type TodoService } from '@moego/api-web/moego/api/todo/v1/todo_api';
import { type UserProfileService } from '@moego/api-web/moego/api/user_profile/v1/user_profile_api';
import { type CustomizeCareTypeService } from '@moego/api-web/moego/api/offering/v1/customize_care_type_api';

import { http } from './api';

import { type ServiceChargeDetailService } from '@moego/api-web/moego/api/appointment/v1/service_charge_detail_api';
import { type EmailService } from '@moego/api-web/moego/api/engagement/v1/email_api';
import { type BookingCareTypeService } from '@moego/api-web/moego/api/online_booking/v1/ob_customize_care_type_api';

export const ActivityLogClient = createHttpClient<ActivityLogService>('moego.api.activity_log.v1.ActivityLogService')(
  http.rpc,
);

export const AppointmentCheckerClient = createHttpClient<AppointmentCheckerService>(
  'moego.api.appointment.v1.AppointmentCheckerService',
)(http.rpc);

export const BusinessClient = createHttpClient<BusinessService>('moego.api.organization.v1.BusinessService')(http.rpc);

export const BusinessCustomerClient = createHttpClient<BusinessCustomerService>(
  'moego.api.business_customer.v1.BusinessCustomerService',
)(http.rpc);

export const BusinessCustomerTagClient = createHttpClient<BusinessCustomerTagService>(
  'moego.api.business_customer.v1.BusinessCustomerTagService',
)(http.rpc);

export const BusinessCustomerReferralSourceClient = createHttpClient<BusinessCustomerReferralSourceService>(
  'moego.api.business_customer.v1.BusinessCustomerReferralSourceService',
)(http.rpc);

export const BusinessCustomerSettingClient = createHttpClient<BusinessCustomerSettingService>(
  'moego.api.business_customer.v1.BusinessCustomerSettingService',
)(http.rpc);

export const BusinessPetCodeClient = createHttpClient<BusinessPetCodeService>(
  'moego.api.business_customer.v1.BusinessPetCodeService',
)(http.rpc);

export const BusinessPetColorClient = createHttpClient<BusinessPetColorService>(
  'moego.api.business_customer.v1.BusinessPetColorService',
)(http.rpc);

export const BusinessPetBehaviorClient = createHttpClient<BusinessPetBehaviorService>(
  'moego.api.business_customer.v1.BusinessPetBehaviorService',
)(http.rpc);

export const BusinessPetFixedClient = createHttpClient<BusinessPetFixedService>(
  'moego.api.business_customer.v1.BusinessPetFixedService',
)(http.rpc);

export const BusinessPetCoatTypeClient = createHttpClient<BusinessPetCoatTypeService>(
  'moego.api.business_customer.v1.BusinessPetCoatTypeService',
)(http.rpc);

export const BusinessPetTypeClient = createHttpClient<BusinessPetTypeService>(
  'moego.api.business_customer.v1.BusinessPetTypeService',
)(http.rpc);

export const BusinessPetBreedClient = createHttpClient<BusinessPetBreedService>(
  'moego.api.business_customer.v1.BusinessPetBreedService',
)(http.rpc);

export const BusinessPetVaccineClient = createHttpClient<BusinessPetVaccineService>(
  'moego.api.business_customer.v1.BusinessPetVaccineService',
)(http.rpc);

export const BusinessPetVaccineRequestClient = createHttpClient<BusinessPetVaccineRequestService>(
  'moego.api.business_customer.v1.BusinessPetVaccineRequestService',
)(http.rpc);

export const BusinessPetSizeClient = createHttpClient<BusinessPetSizeService>(
  'moego.api.business_customer.v1.BusinessPetSizeService',
)(http.rpc);

export const BusinessCustomerPreferredFrequencyClient = createHttpClient<BusinessCustomerPreferredFrequencyService>(
  'moego.api.business_customer.v1.BusinessCustomerPreferredFrequencyService',
)(http.rpc);

export const CalendarClient = createHttpClient<CalendarService>('moego.api.appointment.v1.CalendarService')(http.rpc);

export const ServiceChargeClient = createHttpClient<ServiceChargeService>('moego.api.order.v1.ServiceChargeService')(
  http.rpc,
);

export const ServiceChargeDetailClient = createHttpClient<ServiceChargeDetailService>(
  'moego.api.appointment.v1.ServiceChargeDetailService',
)(http.rpc);

export const ServiceChargeCompanyClient = createHttpClient<ServiceChargeCompanyService>(
  'moego.api.order.v1.ServiceChargeCompanyService',
)(http.rpc);

export const ScheduleMessageClient = createHttpClient<ScheduleMessageService>(
  'moego.api.message.v1.ScheduleMessageService',
)(http.rpc);

export const AutoMessageClient = createHttpClient<AutoMessageTemplateService>(
  'moego.api.message.v1.AutoMessageTemplateService',
)(http.rpc);

export const DiscountCodeClient = createHttpClient<DiscountCodeService>('moego.api.marketing.v1.DiscountCodeService')(
  http.rpc,
);

export const OrderDiscountClient = createHttpClient<OrderDiscountCodeService>(
  'moego.api.order.v1.OrderDiscountCodeService',
)(http.rpc);

export const CompanyClient = createHttpClient<CompanyService>('moego.api.organization.v1.CompanyService')(http.rpc);

export const NotificationClient = createHttpClient<NotificationService>(
  'moego.api.notification.v1.NotificationService',
)(http.rpc);

export const PermissionClient = createHttpClient<PermissionService>('moego.api.permission.v1.PermissionService')(
  http.rpc,
);

export const StaffClient = createHttpClient<StaffService>('moego.api.organization.v1.StaffService')(http.rpc);

export const StaffTrackingClient = createHttpClient<StaffTrackingService>(
  'moego.api.organization.v1.StaffTrackingService',
)(http.rpc);

export const MessageTemplateClient = createHttpClient<MessageTemplateService>(
  'moego.api.message.v1.MessageTemplateService',
)(http.rpc);

export const AccountInfoClient = createHttpClient<AccountInfoService>('moego.api.account.v1.AccountInfoService')(
  http.rpc,
);

export const ServiceManagementClient = createHttpClient<ServiceManagementService>(
  'moego.api.offering.v1.ServiceManagementService',
)(http.rpc);

export const ServiceStaffOverrideRuleClient = createHttpClient<ServiceStaffOverrideRuleService>(
  'moego.api.offering.v1.ServiceStaffOverrideRuleService',
)(http.rpc);

export const ServiceStaffClient = createHttpClient<ServiceStaffService>('moego.api.offering.v1.ServiceStaffService')(
  http.rpc,
);

export const BusinessPetMetadataClient = createHttpClient<BusinessPetMetadataService>(
  'moego.api.business_customer.v1.BusinessPetMetadataService',
)(http.rpc);

export const BusinessPetFeedingScheduleClient = createHttpClient<BusinessPetFeedingScheduleService>(
  'moego.api.business_customer.v1.BusinessPetFeedingScheduleService',
)(http.rpc);

export const BusinessPetMedicationScheduleClient = createHttpClient<BusinessPetMedicationScheduleService>(
  'moego.api.business_customer.v1.BusinessPetMedicationScheduleService',
)(http.rpc);

export const LodgingTypeClient = createHttpClient<LodgingTypeService>('moego.api.offering.v1.LodgingTypeService')(
  http.rpc,
);

export const LodgingUnitClient = createHttpClient<LodgingUnitService>('moego.api.offering.v1.LodgingUnitService')(
  http.rpc,
);

export const LodgingClient = createHttpClient<LodgingService>('moego.api.appointment.v1.LodgingService')(http.rpc);

export const OverviewClient = createHttpClient<OverviewService>('moego.api.appointment.v1.OverviewService')(http.rpc);
export const AgreementClient = createHttpClient<AgreementService>('moego.api.agreement.v1.AgreementService')(http.rpc);
export const AppointmentScheduleClient = createHttpClient<AppointmentScheduleService>(
  'moego.api.appointment.v1.AppointmentScheduleService',
)(http.rpc);
export const AccountAccessClient = createHttpClient<AccountAccessService>('moego.api.account.v1.AccountAccessService')(
  http.rpc,
);

export const BusinessConversationClient = createHttpClient<BusinessConversationService>(
  'moego.api.ai_assistant.v1.BusinessConversationService',
)(http.rpc);

export const OrderClient = createHttpClient<OrderService>('moego.api.order.v1.OrderService')(http.rpc);

export const OrderV2Client = createHttpClient<OrderServiceV2>('moego.api.order.v2.OrderService')(http.rpc);

export const SplitTipsClient = createHttpClient<SplitTipsService>('moego.api.order.v1.SplitTipsService')(http.rpc);

export const MarketingEmailClient = createHttpClient<MarketingEmailService>(
  'moego.api.message.v1.MarketingEmailService',
)(http.rpc);

export const GoogleReserveIntegrationClient = createHttpClient<GoogleReserveIntegrationService>(
  'moego.api.google_partner.v1.GoogleReserveIntegrationService',
)(http.rpc);

export const MetadataApiClient = createHttpClient<MetadataApiService>('moego.api.metadata.v1.MetadataApiService')(
  http.rpc,
);

export const EvaluationClient = createHttpClient<EvaluationService>('moego.api.offering.v1.EvaluationService')(
  http.rpc,
);

export const PetDetailClient = createHttpClient<PetDetailService>('moego.api.appointment.v1.PetDetailService')(
  http.rpc,
);

export const PetNoteClient = createHttpClient<BusinessPetNoteService>(
  'moego.api.business_customer.v1.BusinessPetNoteService',
)(http.rpc);

export const ReportClient = createHttpClient<ReportService>('moego.api.reporting.v2.ReportService')(http.rpc);

export const CustomReportClient = createHttpClient<CustomReportService>('moego.api.reporting.v2.CustomReportService')(
  http.rpc,
);

export const ReportAttributeClient = createHttpClient<AttributeService>('moego.api.reporting.v2.AttributeService')(
  http.rpc,
);

export const DashboardClient = createHttpClient<DashboardService>('moego.api.reporting.v2.DashboardService')(http.rpc);

export const FileClient = createHttpClient<FileService>('moego.api.file.v2.FileService')(http.rpc);

export const TranscodeClient = createHttpClient<TranscodeService>('moego.api.file.v2.TranscodeService')(http.rpc);

export const TodoClient = createHttpClient<TodoService>('moego.api.todo.v1.TodoService')(http.rpc);

export const BookingRequestServiceClient = createHttpClient<BookingRequestService>(
  'moego.api.online_booking.v1.BookingRequestService',
)(http.rpc);

export const OBAvailabilitySettingClient = createHttpClient<OBAvailabilitySettingService>(
  'moego.api.online_booking.v1.OBAvailabilitySettingService',
)(http.rpc);

export const OBStaffAvailabilityServiceClient = createHttpClient<OBStaffAvailabilityService>(
  'moego.api.online_booking.v1.OBStaffAvailabilityService',
)(http.rpc);

export const MembershipClient = createHttpClient<MembershipService>('moego.api.membership.v1.MembershipService')(
  http.rpc,
);

export const SubscriptionClient = createHttpClient<SubscriptionService>('moego.api.membership.v1.SubscriptionService')(
  http.rpc,
);

export const AutoMessageServiceClient = createHttpClient<AutoMessageService>(
  'moego.api.auto_message.v1.AutoMessageService',
)(http.rpc);

export const LoanClient = createHttpClient<LoanService>('moego.api.capital.v1.LoanService')(http.rpc);
export const FinanceSubscriptionClient = createHttpClient<CommonSubscriptionService>(
  'moego.api.subscription.v1.SubscriptionService',
)(http.rpc);
export const AppointmentServiceClient = createHttpClient<AppointmentService>(
  'moego.api.appointment.v1.AppointmentService',
)(http.rpc);

export const PetServiceClient = createHttpClient<PetService>('moego.api.offering.v1.PetService')(http.rpc);

export const DailyReportClient = createHttpClient<DailyReportService>('moego.api.appointment.v1.DailyReportService')(
  http.rpc,
);

export const GroomingReportClient = createHttpClient<GroomingReportService>(
  'moego.api.grooming.v1.GroomingReportService',
)(http.rpc);

export const UserProfileClient = createHttpClient<UserProfileService>('moego.api.user_profile.v1.UserProfileService')(
  http.rpc,
);

export const BrandedAppServiceClient = createHttpClient<BrandedAppService>(
  'moego.api.branded_app.v1.BrandedAppService',
)(http.rpc);

export const CashDrawerServiceClient = createHttpClient<CashDrawerService>(
  'moego.api.finance_tools.v1.CashDrawerService',
)(http.rpc);

export const EngagementSettingClient = createHttpClient<SettingService>('moego.api.engagement.v1.SettingService')(
  http.rpc,
);
export const MapClient = createHttpClient<MapService>('moego.api.map.v1.MapService')(http.rpc);

export const pricingRuleClient = createHttpClient<PricingRuleService>('moego.api.offering.v1.PricingRuleService')(
  http.rpc,
);

export const AccountingClient = createHttpClient<AccountingService>('moego.api.accounting.v1.AccountingService')(
  http.rpc,
);

export const EngagementCallingLogClient = createHttpClient<CallingService>('moego.api.engagement.v1.CallingService')(
  http.rpc,
);
export const CustomerAvailabilityClient = createHttpClient<CustomerAvailabilityService>(
  'moego.api.online_booking.v1.CustomerAvailabilityService',
)(http.rpc);

export const AutomationClient = createHttpClient<AutomationService>('moego.api.online_booking.v1.AutomationService')(
  http.rpc,
);

export const WorkflowClient = createHttpClient<WorkflowService>('moego.api.automation.v1.WorkflowService')(http.rpc);

export const SmartScheduleSettingServiceClient = createHttpClient<SmartScheduleSettingService>(
  'moego.api.smart_scheduler.v1.SmartScheduleSettingService',
)(http.rpc);

export const AppointmentTaskClient = createHttpClient<AppointmentTaskService>(
  'moego.api.appointment.v1.AppointmentTaskService',
)(http.rpc);

export const PrintCardClient = createHttpClient<PrintCardService>('moego.api.appointment.v1.PrintCardService')(
  http.rpc,
);

export const PaymentClient = createHttpClient<PaymentService>('moego.api.payment.v2.PaymentService')(http.rpc);

export const PaymentOnboardClient = createHttpClient<PaymentOnboardService>(
  'moego.api.payment.v2.PaymentOnboardService',
)(http.rpc);

export const CustomerPackageClient = createHttpClient<CustomerPackageService>(
  'moego.api.customer.v1.CustomerPackageService',
)(http.rpc);

export const PackageClient = createHttpClient<PackageService>('moego.api.promotion.v1.PackageService')(http.rpc);

export const OrderAssignItemClient = createHttpClient<AssignItemAmountApiService>(
  'moego.api.order.v1.AssignItemAmountApiService',
)(http.rpc);

export const BusinessPetIncidentTypeClient = createHttpClient<BusinessPetIncidentTypeService>(
  'moego.api.business_customer.v1.BusinessPetIncidentTypeService',
)(http.rpc);

export const BusinessPetIncidentReportClient = createHttpClient<BusinessPetIncidentReportService>(
  'moego.api.business_customer.v1.BusinessPetIncidentReportService',
)(http.rpc);

export const CustomerRetentionClient = createHttpClient<BusinessCustomerRetentionService>(
  'moego.api.business_customer.v1.BusinessCustomerRetentionService',
)(http.rpc);

export const PetPlaygroupClient = createHttpClient<PetPlaygroupService>('moego.api.appointment.v1.PetPlaygroupService')(
  http.rpc,
);

export const PlaygroupClient = createHttpClient<PlaygroupService>('moego.api.offering.v1.PlaygroupService')(http.rpc);

export const CameraIntegrationClient = createHttpClient<CameraService>('moego.api.organization.v1.CameraService')(
  http.rpc,
);

export const BusinessPetEvaluationClient = createHttpClient<BusinessPetEvaluationService>(
  'moego.api.business_customer.v1.BusinessPetEvaluationService',
)(http.rpc);

export const PricingRuleServiceV2Client = createHttpClient<PricingRuleServiceV2>(
  'moego.api.offering.v2.PricingRuleService',
)(http.rpc);

export const WaitlistServiceClient = createHttpClient<WaitlistService>('moego.api.online_booking.v1.WaitlistService')(
  http.rpc,
);

export const GroupClassClient = createHttpClient<GroupClassService>('moego.api.offering.v1.GroupClassService')(
  http.rpc,
);

export const FulfillmentClient = createHttpClient<FulfillmentService>('moego.api.fulfillment.v1.FulfillmentService')(
  http.rpc,
);

export const ServiceChargeDetailServiceClient = createHttpClient<ServiceChargeDetailService>(
  'moego.api.appointment.v1.ServiceChargeDetailService',
)(http.rpc);

export const PromotionServiceClient = createHttpClient<PromotionService>('moego.api.promotion.v1.PromotionService')(
  http.rpc,
);
export const CheckInOutAlertClient = createHttpClient<CheckInOutAlertService>(
  'moego.api.appointment.v1.CheckInOutAlertService',
)(http.rpc);

export const CustomizedCareTypeClient = createHttpClient<CustomizeCareTypeService>(
  'moego.api.offering.v1.CustomizeCareTypeService',
)(http.rpc);

export const TimeSlotServiceClient = createHttpClient<TimeSlotService>('moego.api.smart_scheduler.v1.TimeSlotService')(
  http.rpc,
);

export const EmailServiceClient = createHttpClient<EmailService>('moego.api.engagement.v1.EmailService')(http.rpc);

export const BookingCareTypeClient = createHttpClient<BookingCareTypeService>(
  'moego.api.online_booking.v1.BookingCareTypeService',
)(http.rpc);
