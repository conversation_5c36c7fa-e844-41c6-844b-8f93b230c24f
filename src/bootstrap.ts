/*
 * @since 2020-09-27 10:40:34
 * <AUTHOR> <<EMAIL>>
 */

import { type Selectable, type Store } from 'amos';
import { type History } from 'history';
import { isNil } from 'lodash';
import { initLogger } from './init/initDatadogLog';
import { initDatadogRUM } from './init/initDatadogRUM';
import { initDayjs } from './init/initDayjs';
import { initHTTP } from './init/initHTTP';
import { initJavaScript } from './init/initJavaScript';
import { CompanyClient } from './middleware/clients';
import { getAccountInfo } from './store/account/account.actions';
import { currentBusinessIdBox } from './store/business/business.boxes';
import { checkCompanyAndBusinessValid, switchCompany } from './store/company/company.actions';
import { currentCompanyIdBox } from './store/company/company.boxes';
import { isLoading, isLogout, isNormal } from './store/utils/identifier';
import { autorun } from './utils/autorun';
import { diagnoseNetworkWhenNavigation } from './utils/diagnoseNetworkWhenNavigation';
import { fixGTMMemoryLeak } from './utils/fixGTMMemoryLeak';
import { parseSearchHeaders, replaceLocation, syncHistory } from './utils/querySessionContext';
import { KEY_BUSINESS_ID, KEY_COMPANY_ID } from './utils/querySessionContextConst';

declare module 'amos' {
  interface ActionOptions<R = any, A extends any[] = any[]> {
    queryDeps?: (...args: A) => Selectable[];
  }
}

declare module 'history' {
  export interface History {
    goBackWithFallback: (fallback: string) => void;
    /**
     * 原始的 history.replace 方法，不会添加 querySession
     */
    _replaceHistoryWithoutSync: History['replace'];
  }
}

if (__DEV__) {
  const debug = {
    underToCamel: (v: any) => {
      if (typeof v !== 'object' || !v) {
        return v;
      }
      for (const k in v) {
        if (k.indexOf('_') > -1) {
          const nk = k.replace(/_+(.)/g, (_, $1) => $1.toUpperCase());
          v[nk] = debug.underToCamel(v[k]);
          delete v[k];
        } else {
          v[k] = debug.underToCamel(v[k]);
        }
      }
      return v;
    },
  };
  Object.assign(window, { debug });
}

export function bootstrap(store: Store, history: History) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  require('rc-tooltip/assets/bootstrap_white.css');

  initDayjs();
  initJavaScript();
  initLogger();
  initHTTP(store, history);
  initDatadogRUM();
  diagnoseNetworkWhenNavigation(history);
  fixGTMMemoryLeak();

  const open = window.open;
  window.open = (url, target, features) => {
    if (!url) {
      return open(url, target, features);
    }
    if (typeof url === 'string') {
      url = new URL(url, location.href);
    }
    if (url.origin === location.origin) {
      const loc = replaceLocation(history, { search: url.search, pathname: url.pathname });
      url.search = loc.search!;
      url.pathname = loc.pathname!;
    } else {
      /**
       * 在打开第三方链接前先 switchCompany 保证 session 与 url 中的 CID 和 BID 一致
       * 避免从第三方跳回后由于 query 丢失导致跳转前后 CID 和 BID 可能发生变化
       * @see https://moegoworkspace.slack.com/archives/C079042STRD/p1722396186159339
       */
      const headers = parseSearchHeaders(history);
      const companyId = isNil(headers[KEY_COMPANY_ID]) ? undefined : Number(headers[KEY_COMPANY_ID]);
      const businessId = isNil(headers[KEY_BUSINESS_ID]) ? undefined : Number(headers[KEY_BUSINESS_ID]);
      if (companyId !== undefined) {
        if (store.dispatch(checkCompanyAndBusinessValid(companyId, businessId))) {
          if (target === '_blank') {
            // 如果是在新标签页打开，当前页面还需要继续使用，需要走switchCompany的流程
            // 不需要 await， 在后台慢慢处理就好
            store.dispatch(switchCompany(companyId, businessId)).then(() => {
              store.dispatch(getAccountInfo());
            });
          } else if (!window.ReactNativeWebView) {
            // react-native-webview 跳过 switchCompany
            // 请求发出去就行，反正当前页面会被销毁
            // 这里更严谨的话需要对 _top 和 _parent 进行处理，不过够用了
            CompanyClient.switchCompany({
              companyId: `${companyId}`,
              businessId: isNil(businessId) ? undefined : `${businessId}`,
            });
          }
        }
      }
    }
    return open(url, target, features);
  };

  store.subscribe(
    autorun(
      () => [store.select(currentCompanyIdBox), store.select(currentBusinessIdBox)] as const,
      (companyId, businessId) => {
        if (isLoading(companyId) || isLoading(businessId) || isLogout(businessId) || isLogout(companyId)) {
          return;
        }

        syncHistory(
          history,
          isNormal(companyId) ? companyId + '' : void 0,
          isNormal(businessId) ? businessId + '' : void 0,
        );
      },
      [],
    ),
  );
}
