/*
 * @since 2020-09-27 10:20:50
 * <AUTHOR> <<EMAIL>>
 */

import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { initDayjs } from './init/initDayjs';

initDayjs();

describe('entrypoint', () => {
  it('should init utc', () => {
    const nowOffset = new Date().getTimezoneOffset();
    const zeroOffset = new Date(1970, 0, 1).getTimezoneOffset();
    expect(dayjs().utcOffset()).toBe(-nowOffset);
    expect(dayjs('1970-01-01 00:00:00').valueOf()).toBe(zeroOffset * T_MINUTE);
    expect(dayjs.utc(0).format('YYYY-MM-DD HH:mm:ss')).toBe('1970-01-01 00:00:00');
    expect(dayjs.utc(new Date(1970, 0, 1)).valueOf()).toBe(zeroOffset * T_MINUTE);
  });
});
