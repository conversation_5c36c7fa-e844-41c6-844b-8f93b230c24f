import { useDispatch, useSelector } from 'amos';
import { Button, Modal } from 'antd';
import axios, { type AxiosResponse } from 'axios';
import React, { memo, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { globalPageStatusBox, updateGlobalPageStatus } from './components/globals/GlobalPageStatus.store';

type VersionData = { revision: string; time: string };

const CHECK_FREQUENCY = 5 * 60 * 1000;

const VERSION_URL = `${location.origin}/output.json`;

const VersionCheckModal = styled(Modal)`
  .ant-modal-content {
    background: #131927;
    color: #fff;
    border-radius: 40px;
  }

  .ant-modal-body {
    padding: 10px 14px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-btn {
    color: #333;
  }
`;

export const VersionCheck = memo<{}>(() => {
  const version = useRef<VersionData | null>(null);
  const dispatch = useDispatch();
  const [pageStatusControl] = useSelector(globalPageStatusBox);
  const reuseVisible = pageStatusControl.reuseVersionCheck && pageStatusControl.visible;
  const [visible, setVisible] = useState(reuseVisible);

  const handleRefresh = () => {
    setVisible(false);
    dispatch(
      updateGlobalPageStatus({
        visible: false,
      }),
    );
    location.reload();
  };

  const disableVisible = () => {
    setVisible(false);
    dispatch(
      updateGlobalPageStatus({
        visible: false,
      }),
    );
  };

  useEffect(() => {
    if (!visible && reuseVisible) {
      setVisible(true);
    }
  }, [reuseVisible, visible]);

  useEffect(() => {
    if (!__DEV__) {
      const timer: ReturnType<typeof setInterval> = setInterval(() => {
        axios
          .request({
            url: VERSION_URL,
            method: 'GET',
            params: {
              timestamp: Date.now(),
            },
          })
          .then((res: AxiosResponse<VersionData>) => {
            const data = res.data;
            if (version.current === null) {
              version.current = { revision: data.revision, time: data.time };
            } else if (!(version.current.revision === data.revision && version.current.time === data.time)) {
              setVisible(true);
              version.current = { revision: data.revision, time: data.time };
            }
          })
          .catch((err) => {
            console.log('version check failed', err);
          });
      }, CHECK_FREQUENCY);

      return () => {
        clearInterval(timer);
      };
    }
    return () => {};
  }, []);
  return (
    <>
      <VersionCheckModal
        visible={visible}
        title={undefined}
        footer={null}
        mask={false}
        maskClosable={true}
        onCancel={disableVisible}
      >
        <p>This page is out of date. Please</p>
        <Button shape="round" type="default" size="middle" onClick={handleRefresh}>
          refresh
        </Button>
        <p>to get the latest version. </p>
      </VersionCheckModal>
    </>
  );
});
