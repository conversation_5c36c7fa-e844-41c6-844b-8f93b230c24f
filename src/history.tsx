import { AlertDialog } from '@moego/ui';
import {
  type Action,
  type BrowserHistoryBuildOptions,
  type History,
  type Location,
  createBrowserHistory,
} from 'history';
import React from 'react';
import SvgIconCloseSvg from './assets/svg/icon-close.svg';
import { SvgIcon } from './components/Icon/Icon';
import { modalApi } from './components/Modal/Modal';
import { DANGEROUSLY_ROUTE_CONFIRM_CONFIG } from './config/history';
import { wrapHistory } from './utils/querySessionContext';

const initialLocationKey = '__INITIAL_LOCATION__';

export const createHistory = (options?: BrowserHistoryBuildOptions): History => {
  const history = createBrowserHistory(options);
  wrapHistory(history);

  // Put a flag on the state of the initial location. We'll check for this flag
  // whenever we need to know whether the current location is the initial one in
  // the browser history.
  history.replace({
    ...history.location,
    state: {
      [initialLocationKey]: true,
    },
  });

  history.goBackWithFallback = (fallbackPath: string) => {
    const state = history.location.state as any;

    // Are we in the initial location? In case so, 'goBack' would originally
    // make us leave the website. Instead, let's use 'replace' to go to the
    // fallback path.
    if (state && state[initialLocationKey] && fallbackPath) {
      history.replace(fallbackPath, state);
      return;
    }

    history.goBack();
  };

  return history;
};

/**
 * please don't use history directly, use `useHistory` instead
 */
export const history = createHistory({
  getUserConfirmation: (message, cb) => {
    const config = DANGEROUSLY_ROUTE_CONFIRM_CONFIG.current;
    const clearConfig = () => (DANGEROUSLY_ROUTE_CONFIRM_CONFIG.current = undefined);
    if (config?.useNewUI) {
      const { newUIModalProps = {} } = config;
      const { onConfirm, onClose, onCancel } = newUIModalProps;
      AlertDialog.open({
        ...newUIModalProps,
        title: newUIModalProps?.title ?? 'Unsaved changes',
        content: newUIModalProps?.content ?? message,
        confirmText: newUIModalProps?.confirmText ?? 'Save',
        cancelText: newUIModalProps?.cancelText ?? 'Discard changes',
        onConfirm: (e) => {
          return Promise.resolve(onConfirm?.(e))
            .then(() => cb(true))
            .catch(() => cb(false))
            .finally(clearConfig);
        },
        onCancel: (e) => {
          return Promise.resolve(onCancel?.(e))
            .then(() => cb(true))
            .catch(() => cb(false))
            .finally(clearConfig);
        },
        onClose() {
          cb(false);
          onClose?.();
          AlertDialog.close();
        },
      });
      return;
    }
    if (config) {
      const leaveConfirm = modalApi.confirm({
        className: 'unsave-double-confirm',
        content: (
          <div className="!moe-tex-[14px] !moe-font-medium !moe-text-[#666] !moe-leading-[18px]">
            You have unsaved changes on this page.
          </div>
        ),
        okText: 'Save and leave',
        cancelText: 'Discard changes',
        ...config,
        title:
          typeof config.title == 'string' ? (
            <div className="!moe-relative !moe-font-bold !moe-text-[18px] !moe-leading-[22px] !moe-text-[#333]">
              {config.title ?? 'Save changes?'}
              <SvgIcon
                className="!moe-absolute !moe-right-[-12px] !moe-top-[-16px]"
                src={SvgIconCloseSvg}
                onClick={(e) => {
                  e.stopPropagation();
                  cb(false);
                  leaveConfirm.destroy();
                  config.onClose?.();
                }}
              />
            </div>
          ) : (
            config.title
          ),
        onOk: (...args: any[]) => {
          if (config.saveAsClose) {
            return config.onClose?.();
          }
          return Promise.resolve(config.onOk?.())
            .then(() => cb(true))
            .catch(() => cb(false))
            .finally(clearConfig);
        },
        onCancel: (...args: any[]) => {
          return Promise.resolve(config.onCancel?.())
            .then(() => cb(true))
            .catch(() => cb(false))
            .finally(clearConfig);
        },
      });
      return;
    }
    modalApi.confirm({
      className: 'unsave-double-confirm',
      title: 'Unsaved changes',
      content: message,
      okText: 'Leave page',
      cancelText: 'Stay on page',
      onOk: () => cb(true),
      onCancel: () => cb(false),
    });
  },
});

type Prompt = Exclude<Parameters<History['block']>[0], undefined>;

interface HistoryMap {
  prompt: Prompt;
  alwaysRun?: boolean;
  hashContent: string;
  remove: () => void;
}

const historyMap = new Map<string, HistoryMap>();

export interface HistoryBlockProps {
  prompt: Prompt;
  alwaysRun?: boolean;
  debug?: boolean;
}

/**
 * 不要直接使用 history.block, 通过 historyBlock 来使用
 *
 * 目的：避免直接使用 history.block 需要注意顺序问题，否则后续的 block 会覆盖前面的 block
 * @param {object} HistoryBlockProps
 * @param {Function} HistoryBlockProps.prompt block 的回调函数
 * @param {boolean} [HistoryBlockProps.alwaysRun] 为 true 时，注册后总是会执行
 * @param {boolean} [HistoryBlockProps.debug] 调试选项，会在执行前输出全部 block 信息，用于辅助查看是否需要开启 alwaysRun
 */
export function historyBlock({ prompt, alwaysRun, debug }: HistoryBlockProps) {
  const hashContent = prompt.toString();

  if (historyMap.has(hashContent)) {
    return historyMap.get(hashContent)!.remove;
  }

  const promptWrapper = (location: Location, action: Action) => {
    // 过滤出 alwaysRun 的 block
    const filterList = [...historyMap.values()]
      .filter((item) => item.alwaysRun && item.hashContent !== hashContent)
      .map((item) => item.prompt);

    // 默认一直执行最新的 block
    const runList = [historyMap.get(hashContent)?.prompt, ...filterList].filter(Boolean) as Prompt[];

    if (debug) {
      console.log('History block list:', runList);
      console.log('All block data:', historyMap.values());
    }
    for (const run of runList) {
      const result = typeof run === 'function' ? run(location, action) : run;

      // 1. 有一个返回 false 全局默认提示用户，并跳过后续的 block
      // 2. 返回 string 时，使用自定义内容提示用户
      if (result === false || typeof result === 'string') {
        return result;
      }
    }

    // 都通过后不拦截, 返回 undefined 或者 true 效果都一样，具体可以看 `react-router-dom.js` 的 `confirmTransitionTo` 的实现，当为 false 和 string 时才会拦截
    return undefined;
  };

  const unsubscribe = history.block(promptWrapper);

  const remove = () => {
    historyMap.delete(hashContent);

    if (historyMap.size === 0) {
      unsubscribe();
    }
  };

  historyMap.set(hashContent, {
    prompt,
    alwaysRun,
    hashContent,
    remove,
  });

  return remove;
}
