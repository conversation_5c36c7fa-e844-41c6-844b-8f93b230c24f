{"compilerOptions": {"paths": {"react": ["./node_modules/@types/react"], "dayjs": ["./node_modules/dayjs"]}, "target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react", "noUnusedParameters": false, "importHelpers": true, "noImplicitThis": true, "noEmitOnError": true, "noImplicitReturns": false, "noImplicitAny": true, "noFallthroughCasesInSwitch": true, "rootDir": "src", "outDir": "lib", "sourceMap": true, "declaration": true, "incremental": true, "tsBuildInfoFile": "node_modules/.cache/tsbuildinfo.json", "noUnusedLocals": true, "downlevelIteration": true, "keyofStringsOnly": false, "preserveValueImports": false, "useUnknownInCatchVariables": false, "types": ["@types/intercom-web", "vitest/globals", "@types/webpack-env"]}, "include": ["src"], "exclude": ["node_modules", "lib"]}