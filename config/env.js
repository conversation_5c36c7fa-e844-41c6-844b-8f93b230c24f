'use strict';

// https://rsbuild.dev/zh/guide/basic/configure-rsbuild#env
// NODE_ENV is cached here
const NODE_ENV_MAP = {
  DEVELOPMENT: 'development',
  TEST: 'test',
  PRODUCTION: 'production',
};

const NODE_ENV = process.env.NODE_ENV;

if (!NODE_ENV) {
  throw new Error('The NODE_ENV environment variable is required but was not specified.');
}

if (!Object.values(NODE_ENV_MAP).includes(NODE_ENV)) {
  throw new Error(`NODE_ENV can only be one of ${Object.values(NODE_ENV_MAP).join(',')}, but received ` + NODE_ENV);
}

const __DEV__ = NODE_ENV === NODE_ENV_MAP.DEVELOPMENT;
const __TEST__ = NODE_ENV === NODE_ENV_MAP.TEST;
const __PRO__ = NODE_ENV === NODE_ENV_MAP.PRODUCTION;

const fs = require('fs');
const path = require('path');
const paths = require('./paths');

delete require.cache[require.resolve(path.resolve(__dirname, './paths'))];

const dotenvFiles = [
  `${paths.dotenv}.${NODE_ENV}.local`,
  `${paths.dotenv}.${NODE_ENV}`,
  `${paths.dotenv}.local`,
  paths.dotenv,
].filter(Boolean);

dotenvFiles.forEach((dotenvFile) => {
  if (fs.existsSync(dotenvFile)) {
    require('dotenv-expand')(
      require('dotenv').config({
        path: dotenvFile,
      }),
    );
  }
});

const appDirectory = fs.realpathSync(process.cwd());
process.env.NODE_PATH = (process.env.NODE_PATH || '')
  .split(path.delimiter)
  .filter((folder) => folder && !path.isAbsolute(folder))
  .map((folder) => path.resolve(appDirectory, folder))
  .join(path.delimiter);

const REACT_APP = /^REACT_APP_/i;

function getClientEnvironment(publicUrl) {
  const raw = Object.keys(process.env)
    .filter((key) => REACT_APP.test(key))
    .reduce(
      (env, key) => {
        env[key] = process.env[key];
        return env;
      },
      {
        __DEV__,
        __TEST__,
        __PRO__,
        NODE_ENV: __TEST__ ? NODE_ENV_MAP.PRODUCTION : NODE_ENV,
        PUBLIC_URL: publicUrl,
        BUILD_ENV: process.env.BUILD_ENV,
        WDS_SOCKET_HOST: process.env.WDS_SOCKET_HOST,
        WDS_SOCKET_PATH: process.env.WDS_SOCKET_PATH,
        WDS_SOCKET_PORT: process.env.WDS_SOCKET_PORT,
        // process.env.NOT_FAST_REFRESH = true to disable fast refresh
        NOT_FAST_REFRESH: process.env.NOT_FAST_REFRESH,
        REDUX_TRACE: process.env.REDUX_TRACE,
        PROXY_API_HOST: process.env.PROXY_API_HOST,
        BUILD_VERSION: getBuildVersion(),
        BUILD_TIME: getBuildTime(),
        __HTTPS__: process.env.HTTPS === 'true',
        __PROXY_FINANCE_KIT__: process.env.PROXY_FINANCE_KIT === 'true',
        __FINANCE_KIT_ROOT__: process.env.FINANCE_KIT_ROOT,
      },
    );
  const stringified = {
    process: {
      env: Object.keys(raw).reduce((env, key) => {
        env[key] = JSON.stringify(raw[key]);
        return env;
      }, {}),
    },
    __DEV__: JSON.stringify(__DEV__),
    __TEST__: JSON.stringify(__TEST__),
    __PRO__: JSON.stringify(__PRO__),
    __CLICK_TO_COMPONENT_EDITOR__: JSON.stringify(process.env.CLICK_TO_COMPONENT_EDITOR || 'vscode'),
  };

  return { raw, stringified };
}

function getBuildTime() {
  return new Date().toJSON().replace(/T|-|:|(\.\d+Z)/g, '');
}

function getBuildVersion() {
  return getCurrentBranchName() + ':' + getCurrentCommitShortHash();
}

function getCurrentBranchName() {
  return process.env.BRANCH_NAME;
}

function getCurrentCommitHash() {
  return require('child_process').execSync('git rev-parse HEAD').toString().trim();
}

function getCurrentCommitShortHash() {
  return getCurrentCommitHash().slice(0, 7);
}

module.exports = getClientEnvironment;
