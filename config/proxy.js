/*
 * @since 2022-02-16 17:28:33
 * <AUTHOR> <<EMAIL>>
 */

const getClientEnvironment = require('./env');

const env = getClientEnvironment();

const DEFAULT_PROXY_TARGET = env.raw.PROXY_API_HOST || 'https://go.t2.moego.dev';

console.log(`Proxy target: ${DEFAULT_PROXY_TARGET}\n`);

module.exports = [
  {
    context: ['/api', '/moego.api'],
    logLevel: 'info',
    target: DEFAULT_PROXY_TARGET,
    router: (req) => {
      const host = req.headers['cookie']
        ?.split(/\s*;\s*/g)
        .find((item) => item.startsWith('MOE_API_HOST='))
        ?.substring('MOE_API_HOST='.length);
      return host || DEFAULT_PROXY_TARGET;
    },
    secure: false,
    changeOrigin: true,
    cookieDomainRewrite: 'localhost',
  },
];
