import { defineConfig } from '@rsbuild/core';
import { pluginBasicSsl } from '@rsbuild/plugin-basic-ssl';
import { pluginLess } from '@rsbuild/plugin-less';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { pluginStyledComponents } from '@rsbuild/plugin-styled-components';
import { pluginTypeCheck } from '@rsbuild/plugin-type-check';
import { sentryWebpackPlugin } from '@sentry/webpack-plugin';
import { RspackManifestPlugin } from 'rspack-manifest-plugin';
import getClientEnvironment from './config/env';
import { createHttp2ProxyMiddlewares, proxyOnRes } from './config/http2-proxy';
import paths from './config/paths';
import proxy from './config/proxy';
import { FinanceKitProxyPlugin } from './plugins/finance-kit-plugin';
import { USE_CURRENT_COLOR, replaceAttrBySelector } from './scripts/svgo-plugins';

const env = getClientEnvironment(paths.publicUrlOrPath.slice(0, -1));
const { __DEV__, __HTTPS__, __PROXY_FINANCE_KIT__ } = env.raw;
const SENTRY_SAAS_AUTH_TOKEN = process.env.SENTRY_SAAS_AUTH_TOKEN;
const { target, logLevel: _log, context: _ctx, ...httpProxyConfig } = proxy[0];

const http2ProxyConfig = {
  secure: false,
  onRes: proxyOnRes,
};
const proxyPrefix = __HTTPS__ ? '^' : '';
const proxyConfig = {
  [`${proxyPrefix}/api`]: {
    ...(__HTTPS__ ? http2ProxyConfig : httpProxyConfig),
    target,
  },
  [`${proxyPrefix}/moego.api`]: {
    ...(__HTTPS__ ? http2ProxyConfig : httpProxyConfig),
    target,
  },
  [`${proxyPrefix}/moego.bff`]: {
    ...(__HTTPS__ ? http2ProxyConfig : httpProxyConfig),
    target,
  },
};

const proxyMiddlewares = createHttp2ProxyMiddlewares(proxyConfig);

export default defineConfig({
  dev: {
    setupMiddlewares: [
      (middleware) => {
        // @ts-expect-error type mismatch
        __HTTPS__ && middleware.unshift(...proxyMiddlewares);
      },
    ],
    progressBar: true,
    lazyCompilation: !process.env.NO_LAZY,
  },
  server: {
    port: 3001,
    ...(!__HTTPS__ && { proxy: proxyConfig }),
  },
  output: {
    distPath: {
      root: 'build',
      media: 'static/media',
      image: 'static/media',
      svg: 'static/media',
      font: 'static/media',
      assets: 'static/media',
    },
    sourceMap: {
      js: __DEV__ ? 'cheap-module-source-map' : 'source-map',
      css: false,
    },
    assetPrefix: env.raw.PUBLIC_URL,
    filenameHash: 'contenthash:16',
    minify: !__DEV__,
  },
  source: {
    alias: {
      // for the convenience of pnpm link @moego/xxx packages
      // and reduce different versions of duplicate libraries
      ...paths.alias,
      amos: './node_modules/@sourcebug/amos',
      worker_threads: false, // 添加 worker_threads fallback
    },
    define: env.stringified,
    tsconfigPath: paths.appTsConfig,
    entry: {
      sentry: {
        import: './src/sentry.ts',
        html: false,
      },
      index: {
        import: './src/index.tsx',
        dependOn: ['sentry'],
      },
    },
  },
  html: {
    template: './public/index.html',
    crossorigin: true,
  },
  plugins: [
    pluginReact(),
    pluginStyledComponents(),
    pluginLess({
      lessLoaderOptions: {
        lessOptions: {
          javascriptEnabled: true,
          math: 'always',
          modifyVars: {
            'primary-color': '#F96B18',
            'link-color': '#F96B18',
            'pagination-item-size-sm': '28px',
          },
        },
      },
    }),
    pluginSass({
      sassLoaderOptions: {
        api: 'legacy',
        implementation: require('sass'),
      },
    }),
    __DEV__ && !process.env.FAST_DEV && pluginTypeCheck(),
    __HTTPS__ && pluginBasicSsl(),
    __PROXY_FINANCE_KIT__ && new FinanceKitProxyPlugin(),
  ].filter(Boolean),
  tools: {
    rspack(config, { addRules }) {
      const originIndex = config.module?.rules?.findIndex(
        (rule) => rule && typeof rule === 'object' && 'test' in rule && rule.test?.toString()?.indexOf('svg') !== -1,
      );
      originIndex && config.module?.rules?.splice(originIndex, 1);

      addRules([
        {
          test: /\.xlsx$/,
          // converts asset to a separate file and exports the URL address.
          type: 'asset/resource',
        },
        {
          test: /[\/\\]svg[\/\\].*\.svg$/,
          use: [
            { loader: 'raw-loader' },
            {
              loader: 'svgo-loader',
              options: {
                plugins: [
                  { removeDimensions: true },
                  {
                    replaceAttrBySelector: replaceAttrBySelector(USE_CURRENT_COLOR),
                  },
                ],
              },
            },
          ],
        },
        {
          test: /^(?!.*\/svg\/).*\.svg$/,
          type: 'asset/resource',
        },
      ]);

      config.plugins = (config.plugins || []).concat(
        [
          !__DEV__ &&
            SENTRY_SAAS_AUTH_TOKEN &&
            sentryWebpackPlugin({
              authToken: SENTRY_SAAS_AUTH_TOKEN,
              org: 'moego-ey',
              project: 'boarding-desktop',
            }),
          !__DEV__ &&
            new RspackManifestPlugin({
              fileName: 'asset-manifest.json',
              publicPath: env.raw.PUBLIC_URL,
              generate: (seed, files, entrypoints) => {
                const manifestFiles = files.reduce((manifest, file) => {
                  manifest[file.name] = file.path;
                  return manifest;
                }, seed);
                const entrypointFiles = entrypoints.index.filter((fileName) => !fileName.endsWith('.map'));

                return {
                  files: manifestFiles,
                  entrypoints: entrypointFiles,
                };
              },
            }),
        ].filter(Boolean),
      );

      config.experiments = {
        ...config.experiments,
        incremental: __DEV__, // 加快 HMR
      };
    },
    htmlPlugin(config) {
      if (typeof config.templateParameters !== 'function') {
        throw new Error('templateParameters must be a function');
      }
      const originTemplateParameters = config.templateParameters.bind(config);
      Object.assign(config, {
        inject: true,
        templateParameters: (compilation, assets, assetTags, options) => {
          return originTemplateParameters(
            compilation,
            assets,
            {
              ...assetTags,
              headTags: assetTags.headTags.map((tag) => {
                if (tag.tagName === 'script' && tag.attributes.src.includes('sentry')) {
                  delete tag.attributes.defer;
                }
                return tag;
              }),
            },
            options,
          );
        },
        attributes: {
          crossorigin: function (tag, _compilation, _index) {
            if (tag.tagName === 'script') {
              return 'anonymous';
            }
            return false;
          },
        },
        buildVersion: env.raw.BUILD_VERSION,
        buildTime: env.raw.BUILD_TIME,
        nodeEnv: env.raw.NODE_ENV,
      });
    },
    cssExtract: {
      pluginOptions: {
        ignoreOrder: false,
        filename: 'static/css/[name].[contenthash].css',
        chunkFilename: 'static/css/async/[name].[contenthash].css',
      },
    },
  },
  performance: {
    removeMomentLocale: true,
    chunkSplit: __DEV__
      ? undefined
      : { strategy: 'split-by-size', /* before minify */ maxSize: 3 * 1024 * 1024, minSize: 800 * 1024 },
    printFileSize: {
      include: (asset) => /\.js$/.test(asset.name),
    },
    // buildCache: __DEV__, // 本地持久化构建缓存
  },
});
