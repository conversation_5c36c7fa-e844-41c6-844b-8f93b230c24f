{
  "typescript.tsdk": "node_modules/typescript/lib",
  "tailwindCSS.experimental.classRegex": [
    ["([\"'`][^\"'`]*.*?[\"'`])", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ],
  "editor.codeActionsOnSave": {
    // Discussion: https://github.com/microsoft/vscode-discussions/discussions/1664
    // Adding quickfix.biome to editor.codeActionsOnSave slows down saving of *.ts files
    // "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit",
    // Have the same effect as quickfix.biome
    "source.fixAll.biome": "explicit",
    "source.fixAll.eslint": "always",
    "source.removeUnusedImports": "explicit"
  },
  "editor.formatOnSave": true,
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome"
  }
}
