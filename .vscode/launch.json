{"version": "0.2.0", "configurations": [{"name": "Launch via NPM", "request": "launch", "runtimeArgs": ["run", "start:vite"], "runtimeExecutable": "pnpm", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"name": "Attach by Process ID", "processId": "${command:PickProcess}", "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"type": "node", "request": "launch", "name": "Debug openApi2Dts script", "runtimeExecutable": "pnpm", "runtimeArgs": ["openapi"], "skipFiles": ["<node_internals>/**"]}]}