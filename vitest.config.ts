import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  define: {
    __DEV__: false,
    __TEST__: true,
  },
  plugins: [react()],
  logLevel: 'error',
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: 'src/setupTests.ts',
    pool: 'threads',
    testTimeout: 10000,
    sequence: {
      hooks: 'list',
    },
    include: ['src/**/*.{test,spec}.{js,jsx,ts,tsx}'],
    silent: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['src/**/*.{js,jsx,ts,tsx}'],
      exclude: ['src/**/*.d.ts', 'node_modules/', 'lib/**/*.{js,jsx,ts,tsx}'],
      thresholds: {
        branches: 0,
        functions: 0,
        lines: 0,
        statements: 0,
      },
    },
    server: {
      deps: {
        inline: true,
      },
    },
    reporters: 'dot',
    alias: {
      react: path.resolve(__dirname, 'node_modules/react'),
      'antd/es': path.resolve(__dirname, 'node_modules/antd/lib'),
      'lodash-es': 'lodash',
    },
  },
  resolve: {
    // Make sure to not transform these patterns to support @moego packages
    conditions: ['import', 'module', 'node'],
    alias: {
      util: 'node:util',
    },
  },
});
