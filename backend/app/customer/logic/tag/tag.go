package tag

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	activityrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_rel"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/tag"
	utils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 逻辑层
type Logic struct {
	tagRepo         tag.ReadWriter
	activityRelRepo activityrel.ReadWriter
}

// New 构造函数
func New() *Logic {
	return &Logic{
		tagRepo:         tag.New(),
		activityRelRepo: activityrel.New(),
	}
}

// Create 创建 Tag
func (l *Logic) Create(ctx context.Context, datum *CreateTagDatum) (int64, error) {
	if datum == nil || datum.CompanyID <= 0 || datum.Name == "" {
		log.ErrorContextf(ctx, "Create Tag params is invalid, datum:%+v", datum)

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
		CompanyID: &datum.CompanyID,
		Names:     []string{datum.Name},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create Tag List same name err, err: %+v", err)

		return 0, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create Tag name is exist, datum: %+v", datum)

		return 0, errs.New(customerpb.ErrCode_ERR_CODE_TAG_NAME_EXIST)
	}

	dbTag := &tag.Tag{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		Name:       datum.Name,
		Sort:       datum.Sort,
		CreateBy:   datum.StaffID,
		UpdateBy:   datum.StaffID,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	if err := l.tagRepo.Create(ctx, dbTag); err != nil {
		return 0, err
	}

	return dbTag.ID, nil
}

// Update 更新 Tag
func (l *Logic) Update(ctx context.Context, datumList []*UpdateTagDatum, companyID int64) error {
	// check
	if companyID <= 0 || len(datumList) == 0 {
		log.ErrorContextf(ctx, "Update Tag params is empty")

		return status.Error(codes.InvalidArgument, "params is empty")
	}

	// conv db entity
	names := make([]string, 0, len(datumList))
	idChangeNameMap := make(map[int64]*UpdateTagDatum, len(datumList))
	updates := make([]*tag.Tag, 0, len(datumList))
	for _, datum := range datumList {
		if datum == nil || datum.TagID <= 0 ||
			(datum.Name == nil && datum.Sort == nil) {
			log.ErrorContextf(ctx, "Update Tag params is invalid, datum:%+v", datum)

			return status.Error(codes.InvalidArgument, "params is invalid")
		}
		dbTag := &tag.Tag{
			ID:         datum.TagID,
			UpdateBy:   datum.StaffID,
			UpdateTime: time.Now(),
		}
		if datum.Name != nil {
			if *datum.Name == "" {
				log.ErrorContextf(ctx, "Update Tag name is empty")

				return status.Error(codes.InvalidArgument, "name is empty")
			}
			dbTag.Name = *datum.Name
			names = append(names, *datum.Name)
			idChangeNameMap[datum.TagID] = datum
		}
		if datum.Sort != nil {
			if *datum.Sort <= 0 {
				log.ErrorContextf(ctx, "Update Tag sort is invalid, sort:%d", *datum.Sort)

				return status.Error(codes.InvalidArgument, "sort is invalid")
			}
			dbTag.Sort = *datum.Sort
		}
		updates = append(updates, dbTag)
	}

	// check names
	if len(names) > 0 {
		existNameList, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
			CompanyID: &companyID,
			Names:     names,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update Tag List same name err, err: %+v", err)

			return err
		}
		for _, existName := range existNameList {
			// existName source is the same update id and name, skip
			if idChangeNameMap[existName.ID] != nil && *idChangeNameMap[existName.ID].Name == existName.Name {
				continue
			}
			log.InfoContextf(ctx, "Update Tag name is exist, names: %+v", names)

			return errs.New(customerpb.ErrCode_ERR_CODE_TAG_NAME_EXIST)
		}
	}

	// save to db
	if err := l.tagRepo.WithTransaction(ctx, func(api tag.ReadWriter) error {
		for _, update := range updates {
			if err := api.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update Tag WithTransaction err, err:%+v", err)

		return err
	}

	return nil
}

// List 查询 Tag 列表
func (l *Logic) List(ctx context.Context, datum *ListTagsDatum) ([]*customerpb.Tag, error) {
	if datum == nil || (datum.CompanyID == nil && datum.CustomerID == nil && len(datum.IDs) == 0) {
		log.ErrorContextf(ctx, "List Tag params is invalid, datum:%+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv filter
	listDatum := &tag.ListTagsDatum{
		CompanyID: datum.CompanyID,
		IDs:       datum.IDs,
	}
	if datum.CustomerID != nil {
		rels, err := l.activityRelRepo.List(ctx, &activityrel.ListActivityRelDatum{
			CustomerID:   datum.CustomerID,
			ActivityType: utils.ToPointer(activityrel.TagActivityRelType),
		})
		if err != nil {
			return nil, err
		}
		if len(rels) == 0 {
			return []*customerpb.Tag{}, nil
		}
		ids := make([]int64, 0, len(rels))
		for _, rel := range rels {
			ids = append(ids, rel.ActivityID)
		}
		if len(listDatum.IDs) > 0 {
			listDatum.IDs = utils.IntersectInt64(listDatum.IDs, ids)
		} else {
			listDatum.IDs = ids
		}
	}
	tags, err := l.tagRepo.List(ctx, listDatum)
	if err != nil {
		return nil, err
	}

	return convTagsPB(tags), nil
}

// Delete 删除 Tag
func (l *Logic) Delete(ctx context.Context, datum *DeleteTagDatum) error {
	if datum == nil || datum.TagID <= 0 {
		log.ErrorContextf(ctx, "Delete Tag params is invali, ddatum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}
	if err := l.tagRepo.Delete(ctx, datum.TagID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

// convTagsPB repo Tag -> pb Tag
func convTagsPB(tags []*tag.Tag) []*customerpb.Tag {
	res := make([]*customerpb.Tag, 0, len(tags))
	for _, t := range tags {
		if t == nil {
			continue
		}
		res = append(res, convTagPB(t))
	}

	return res
}

func convTagPB(t *tag.Tag) *customerpb.Tag {
	return &customerpb.Tag{
		Id:   t.ID,
		Name: t.Name,
		Sort: t.Sort,
	}
}
