# MoeGo Boarding Desktop

This project is about MoeGo system. It contains the Boarding and Grooming.

## Usage

```sh
pnpm dev
FAST_DEV=1 pnpm dev # disable ts check to decrease memory use.
NO_LAZY=1 pnpm dev # disable lazyCompilation mode

# 辅助工具
pnpm check:deps # 检测是否有循环引用 -T 代表忽略了类型文件的引用关系
pnpm check:useless # 检测项目中没有用到的内容，帮助你安全移除它们
pnpm generate # 添加图标后，生成对应的 ts 定义
pnpm doctor # 帮助你优化构建产物
```

## Tips

1. dev with [whistle](https://github.com/avwo/whistle) [reference](https://mengshikeji.feishu.cn/wiki/XvCkwLb78ingziko3IgcSvaBndh) `新人必看` `recommended`.

2. dev with [redux trace](https://github.com/zalmoxisus/redux-devtools-extension/blob/master/docs/Features/Trace.md)

   ```sh
   # 1. set the REDUX_TRACE env before run dev script
   REDUX_TRACE=true pnpm dev
   ```

3. 快速页面跳转到源码，dev only

   ```sh
   # shift + option + click
   ```

4. 如果需要检查文案，请在 `.env` 文件中设置 `USE_TEXT_LINT=TRUE`，然后运行 `pnpm check:text-lint` 检查文案。

   本地提交也会通过 `pnpm check:text-lint` 检查文案，如果文案有拼写/语法错误，会报错，请根据错误信息修改文案。

   - 如果需要忽略检查
     JsxText 中请在文案上添加 `/* @text-lint ignore */` 注释。

   ```tsx
   <Text>
     {/* @text-lint ignore */}
     campaign messages per client within
   </Text>
   ```

   attribute 中请在文案上添加 `// @text-lint ignore` 注释。

   ```tsx
   <div
     // @text-lint ignore
     title="campaign messages per client within"
   ></div>
   ```

   代码块忽略检查请分别在首和尾添加 `/* @text-lint ignore start */` 和 `/* @text-lint ignore end */` 注释。

   ```tsx
   /* @text-lint ignore start */
   ...your code...
   /* @text-lint ignore end */
   ```

有问题可参考[这里](https://moegoworkspace.slack.com/archives/C02L7M55Z09/p1740361026556509)或 @Channon。

## API Definitions

> 历史因素，后端服务主要有 REST API 和 gRPC API 两种类型，目前均通过 HTTP 接口调用，方向上会一步步往 gRPC 服务去迁移。对应的，前后端 API 定义的同步有两种方案，gRPC 与 REST

1. gRPC API：后端新 gRPC 服务使用这个方案，核心定义在 [moego-api-definitions](https://github.com/MoeGolibrary/moego-api-definitions) 仓库

   - 前端通过 `@moego/api-web` npm 包来引用，在 `src/middleware/clients.ts` 查看使用方式
   - ~~前端基于 `moego-api-definitions` 生成 swagger 格式的定义 `swagger-rpc.ts`（已废弃）~~

```sh
  # 1. 特定的分支环境，以前缀区分，以 `mass-email` 为例
  pnpm i @moego/api-web@mass-email

  # 2. 使用最新的api 定义
  pnpm i @moego/api-web
```

2. REST API：存量 REST 服务的迭代更新走 REST

   - [OpenAPI](https://www.openapis.org/): 当前使用的方案
   - ~~[Swagger](https://swagger.io/)（已废弃）~~
   - ~~[Rapper](https://github.com/thx/rapper)（已废弃）~~

```sh
  # 1. 特定的分支环境，以前缀区分，以 `mass-email` 为例
  npm run openapi -- --prefix=mass-email

  # 2. 使用默认的 T2 环境 `production` backend branch (t2)
  npm run openapi

  # 3. use `staging` backend branch (s1)
  npm run openapi -- --staging
```

## Structure

```sh
├── README.md
├── Standard.md
├── ci
├── commitlint.config.js
├── config # build config
├── guide.md
├── index.html
├── vitest.config.ts
├── nginx.conf
├── package.json
├── pnpm-lock.yaml
├── postcss.config.js
├── public
├── rsbuild.config.ts # dev only
├── scripts # custom by cra eject
├── src # source code
├── tailwind.config.js
├── tsconfig.json
├── tsconfig.test.json
```

## 业务 Reference

[快速了解 Calendar card](./src/store/calendarLatest/README.md)
