/*
 * @since 2020-11-13 10:51:06
 * <AUTHOR> <<EMAIL>>
 */

const G = require('glob');
const fs = require('fs');
const path = require('path');
const cp = require('child_process');

cp.execSync('git checkout ./src && git clean -xfd s./rc && git add ./scripts');

if (process.argv.length !== 3 || process.argv[2].startsWith('-')) {
  console.log('Usage: %s path', process.argv[1]);
  process.exit(1);
}

const context = process.argv[2];
const files = G.sync('**/*.*', { cwd: context });
const styleRoot = 'src/style/style.scss';

function isFile(file) {
  try {
    const stat = fs.statSync(file);
    return stat.isFile();
  } catch {
    return false;
  }
}

function couldResolve(file) {
  return isFile(file) || isFile(file + '.ts') || isFile(file + '.tsx');
}

files
  .map((file) => {
    const filename = path.join(context, file);
    const [, prefix, directory, ext] = filename.match(/^(.*)\/(\w+)\/_?(?:index|style)\.([^\/]+)$/) || [];
    if (ext) {
      const target = `${prefix}/${directory}/${directory}.${ext}`;
      if ((fs.existsSync(target) && filename !== styleRoot) || filename === 'src/components/index.tsx') {
        fs.unlinkSync(filename);
        return;
      } else {
        fs.renameSync(filename, target);
        return target;
      }
    }
    return filename;
  })
  .filter(Boolean)
  .forEach((filename) => {
    const _content = fs.readFileSync(filename, 'utf8');
    let newContent = _content;
    if (!/\.d\.ts$/.test(filename) && /\.tsx?$/.test(filename)) {
      const [, name] = newContent.match(/\nexport default (\w+)/) || [];
      if (name) {
        newContent = newContent
          .replace(`\nconst ${name}`, `\nexport const ${name}`)
          .replace(`export default ${name};`, '');
      }
      if (/export default {/.test(newContent)) {
        let base = path.basename(filename).split('.')[0];
        if (base === 'package') {
          base = 'packages';
        }
        newContent = newContent.replace('export default ', `export const ${base} = `);
      }
      if (!filename.endsWith('/api.ts') && !filename.endsWith('/Upload.tsx')) {
        newContent = newContent.replace(/axios/g, 'http');
      }
      newContent = newContent
        .replace(/(import [^;]*? from ')@(components\/.*?')/g, ($0, $1, $2) => {
          const relative = '../'.repeat(filename.split('/').length - 2);
          return `${$1}${relative}${$2}`;
        })
        .replace(/(import [^;]*? from )'(src\/.*?)'/g, ($0, $1, $2) => {
          const relative = path.relative(path.dirname(filename), $2);
          return `${$1}'${relative}'`;
        })
        .replace(/import ([^{*;}]*?) from ('\..*?')/g, ($0, $1, $2) => {
          if (/\.(svg|png|jpe?g)/i.test($2)) {
            return $0;
          }
          return `import { ${$1} } from ${$2}`;
        })
        .replace(/(import [^;]*? from )'(\..*?)'/g, (all, imp, src) => {
          const target = path.join(path.dirname(filename), src);
          if (!couldResolve(target)) {
            const next = path.join(target, path.basename(target));
            if (couldResolve(next)) {
              return `${imp}'${src}/${path.basename(src)}'`;
            } else {
              const [, keys] = imp.match(/{([^}]+)}/) || [];
              if (keys) {
                return keys
                  .trim()
                  .split(/,\s*/g)
                  .map((expr) => {
                    const item = expr.split(' as ')[0];
                    const next = path.join(target, item, item);
                    if (couldResolve(next)) {
                      return `import { ${expr} } from '${src}/${item}/${item}'`;
                    } else if (couldResolve(path.join(path.dirname(target), item, item))) {
                      return `import { ${expr} } from '${path.dirname(src)}/${item}/${item}'`;
                    } else {
                      console.log('miss %s -> %s:%s', filename, item, src);
                      return `import { ${expr} } from '${src}'`;
                    }
                  })
                  .join(';\n');
              }
              console.log('miss %s -> %s', filename, next);
            }
          }
          return all;
        })
        .replace(/Alert\.api/g, 'alertApi')
        .replace(/{ Alert }(.*?\/)Alert'/g, "{ alertApi }$1AlertApi'")
        .replace(/ from '(\..*?)';\n/g, ($0, $1) => {
          let relative = path.relative(path.dirname(filename), path.join(path.dirname(filename), $1));
          if (!relative.startsWith('.')) {
            relative = `./${relative}`;
          }
          return ` from '${relative}';\n`;
        });
      if (_content !== newContent) {
        fs.writeFileSync(filename, newContent);
      }
    }
  });

fs.writeFileSync(styleRoot, fs.readFileSync(styleRoot, 'utf8').replace(/(\w+)\/style/g, '$1/$1'));
