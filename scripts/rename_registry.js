/*
 * @since 2021-01-29 19:23:50
 * <AUTHOR> <<EMAIL>>
 */

const cp = require('child_process');
const fs = require('fs');

let lock = fs.readFileSync('./yarn.lock', 'utf8');

const useTaobao = cp.execSync('npm config get registry', { encoding: 'utf-8' }).trim().indexOf('taobao') > -1;

console.log(
  'If you %swant to use taobao as the registry, please %s npm registry and then run the script.',
  useTaobao ? 'do not ' : '',
  useTaobao ? 'delete' : 'set',
);

if (!useTaobao) {
  lock = lock.replace(
    /registry\.npm\.taobao\.org\/(.*?)\/download.*?\/([^\/?]+\.tgz).*?#([a-f0-9]+)/g,
    (_, pkg, basename, digest) => {
      return `registry.yarnpkg.com/${pkg}/-/${basename}#${digest}`;
    },
  );
  lock = lock.replace(
    /registry\.nlark\.com\/(.*?)\/download.*?\/([^\/?]+\.tgz).*?#([a-f0-9]+)/g,
    (_, pkg, basename, digest) => {
      return `registry.yarnpkg.com/${pkg}/-/${basename}#${digest}`;
    },
  );
} else {
  lock = lock.replace(
    /registry\.yarnpkg\.com\/(?:(@[^\/?#]+)\/)?([^\/?#]+)\/-\/([^\/?#]+\.tgz).*?#([a-f0-9]+)/g,
    (_, org, pkg, basename, digest) => {
      return `registry.npm.taobao.org/${org ? org + '/' + pkg : pkg}/download/${
        org ? org + '/' : ''
      }${basename}#${digest}`;
    },
  );
}

fs.writeFileSync('./yarn.lock', lock);
