#!/usr/bin/env bash

for file in "$@"; do
  mkdir -p "$(dirname "$file")"
  case "$file" in
    *.ts|*.tsx|*.js|*.jsx)
      echo "/*
 * @since $(date +'%Y-%m-%d %H:%M:%S')
 * <AUTHOR> config user.name) <$(git config user.email)>
 */" > "$file"
      if [[ "$file" = *.tsx ]]; then
        filename="$(basename -s ".tsx" "$file")"
        imports=""
        view="div"
        if [[ -f "$(dirname "$file")/$filename.style.ts" ]]; then
          imports="\nimport { ${filename}View } from './${filename}.style';"
          view="${filename}View"
        fi
        echo -e "
import React, { memo } from 'react';${imports}

export interface ${filename}Props {
  className?: string;
}

export const $filename = memo<${filename}Props>(({ className }) => {
  return (
    <$view className={className}>
    </$view>
  )
});" >> "$file"
      fi
      if [[ "$file" = *.style.ts ]]; then
        filename="$(basename -s ".style.ts" "$file")"
        echo "
import styled from 'styled-components';

export const ${filename}View = styled.div\`
\`;" >> "$file"
      fi
      ;;
    *.sh)
      echo "#!/usr/bin/env bash
# @since $(date +'%Y-%m-%d %H:%M:%S')
# <AUTHOR> config user.name) <$(git config user.email)>
" > "$file"
      chmod +x "$file"
      ;;
    *.go)
      echo "// @since $(date +'%Y-%m-%d %H:%M:%S')
// <AUTHOR> config user.name) <$(git config user.email)>

package $(basename $(dirname "$file"))" > "$file"
      ;;
    *.proto)
      echo "// @since $(date +'%Y-%m-%d %H:%M:%S')
// <AUTHOR> config user.name) <$(git config user.email)>

syntax = \"proto3\";

package $(basename $(dirname "$file"))" > "$file"
      ;;
    *.json)
      echo "{}" > "$file"
      ;;
    esac
  git add "$file"
done
