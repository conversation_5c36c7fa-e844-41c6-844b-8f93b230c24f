/*
 * @since 2020-09-22 15:56:28
 * <AUTHOR> <<EMAIL>>
 */

const cp = require('child_process');
const fs = require('fs-extra');

async function main() {
  if (!process.argv.includes('--no-fetch')) {
    cp.execSync(
      `
mkdir -p ../moego-api
cd ../moego-api
if [ ! -d .git ]; then
  git init
  git remote <NAME_EMAIL>:MoeGolibrary/moego-api.git
fi
git fetch origin master
git checkout origin/master
  `,
      { stdio: 'inherit' },
    );
  }
  const content = await fs.readFile(
    '../moego-api/MoegoCommon/src/main/java/com/moego/common/enums/ResponseCodeEnum.java',
    'utf8',
  );
  let output = `/* This file is generated by ${JSON.stringify(process.argv.join(' '))}, please do not edit it. */

import { createEnum } from '../store/utils/createEnum';

export const ErrorCodes = createEnum({
`;
  output += content
    .split('\n')
    .map((l) => {
      const match = l.match(/([A-Z_]+)\s*\((\d+),\s*"([^"]+)"/);
      if (!match) {
        return '';
      }
      if (match[1] === 'EMAIL_NOT_EXIST') {
        match[3] = 'Account does not exist.';
      } else if (match[1] === 'PASSWORD_ERROR') {
        match[3] = 'Email or password error, please input correct information.';
      }
      return `  ${match[1]}: [${match[2]}, ${JSON.stringify(match[3])}],`;
    })
    .concat(["  INTERNAL: [0, 'System busy, please try again later.'],"])
    .filter(Boolean)
    .sort()
    .join('\n');
  output += '\n});\n';
  const filename = './src/middleware/codes.ts';
  await fs.outputFile(filename, output);
  console.log('Error codes generate success, write to %s', filename);
}

main();
