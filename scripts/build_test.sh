#!/usr/bin/env bash
# @since 2020-08-18 14:17:45
# <AUTHOR> <EMAIL>


set -xeu

GIT_BRANCH="$(git branch | grep '*' | awk '{print $NF}')"

git checkout master
git pull -r origin master
git merge --no-edit "$GIT_BRANCH"
git push origin master

echo "Build and deploy to test server..."

pnpm build:test

scp -q -r build/* root@129.204.41.220:/data/wwwroot/Boarding_Desktop_Release

rm -rf build

git checkout "$GIT_BRANCH"
