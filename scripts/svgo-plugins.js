/*
 * @since 2020-09-18 16:02:46
 * <AUTHOR> <<EMAIL>>
 */

const cssAdapter = require('svgo/lib/svgo/css-select-adapter');

cssAdapter.hasAttrib = (elem, name) => !!elem.attr(name);

exports.removeElementBySelector = {
  type: 'perItem',
  active: true,
  description: 'removes arbitrary elements by selector',
  params: {
    selectors: [],
    excludes: [],
  },
  fn: (item, { selectors, excludes }) => {
    return !selectors.some((s) => item.matches(s)) || excludes.some((s) => item.matches(s));
  },
};

exports.TRANSPARENT_PATH = {
  selectors: ['path:not([fill]):not([stroke])'],
  excludes: ['[fill] path', '[stroke] path'],
};

exports.replaceAttrBySelector = ({ attrs }) => ({
  type: 'perItem',
  active: true,
  description: 'replace attr by selector',
  fn: (item) => {
    attrs.forEach((attr) => {
      if (item.matches(attr.match)) {
        item.addAttr({ ...attr, local: '', prefix: '' });
      }
    });
  },
});

exports.USE_CURRENT_COLOR = {
  attrs: [
    {
      name: 'stroke',
      value: 'currentColor',
      match: '[stroke]:not([stroke="none"]):not([class*="ignoreCurrentColor"])',
    },
    { name: 'fill', value: 'currentColor', match: '[fill]:not([fill="none"]):not([class*="ignoreCurrentColor"])' },
  ],
};
