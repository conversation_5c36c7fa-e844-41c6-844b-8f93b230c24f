/*
 * @since 2020-08-20 14:47:04
 * <AUTHOR> <<EMAIL>>
 */

const fs = require('fs-extra');

function singleArray(input, wrap = true, jsonify = true) {
  if (wrap && input.code === void 0) {
    input = { code: 200, message: '', data: input };
  }
  if (Array.isArray(input)) {
    if (typeof input[0] === 'object') {
      singleArray(Object.assign(...input), false, false);
      input.length = 1;
    } else if (input.length === 1) {
      input.push(input[0]);
    }
  } else if (typeof input === 'object' && input) {
    for (const k in input) {
      if (input.hasOwnProperty(k)) {
        singleArray(input[k], false, false);
      }
    }
  }
  if (jsonify) {
    console.log(JSON.stringify(input));
  } else {
    return input;
  }
}

let used = false;

function singleArrayCmd(ok, ...args) {
  if (used) {
    return;
  }
  used = used || ok;
  ok && singleArray(...args);
}

exports.showMutations = function showMutations(dom = document.body) {
  const observer = new MutationObserver((mutations, observer) => {
    for (const mutation of mutations) {
      switch (mutation.type) {
        case 'attributes':
          console.log(
            'mutation.attribute',
            mutation.attributeName,
            mutation.target.getAttribute(mutation.attributeName),
            mutation.target,
          );
          break;
        case 'characterData':
          console.log('mutation.text', mutation.target.textContent);
          break;
        case 'childList':
          for (const node of mutation.addedNodes) {
            if (node instanceof Element) {
              console.log('mutation.add.element', node.tagName, node.className, node);
            } else {
              console.log('mutation.add.text', node.textContent, node);
            }
          }
          for (const node of mutation.removedNodes) {
            if (node instanceof Element) {
              console.log('mutation.remove.element', node.tagName, node.className, node);
            } else {
              console.log('mutation.remove.text', node.textContent, node);
            }
          }
          break;
      }
    }
  });
  observer.observe(dom, { attributes: true, characterData: true, subtree: true, childList: true });
  return observer;
};

exports.singleArray = singleArray;

function wrapFn(t) {
  return t === '(...args: any[]) => any' ? '(' + t + ')' : t;
}

function js2dts(data, indent = '', path = '', options = {}) {
  if (data === null) {
    return 'null';
  }
  const type = typeof data;
  switch (type) {
    case 'number':
    case 'string':
    case 'boolean':
    case 'undefined':
    case 'bigint':
    case 'symbol':
      return type;
    case 'function':
      return '(...args: any[]) => any';
  }
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return 'unknown[]';
    }
    const types = new Set();
    /** @type Map<string, string> */
    let objType;
    const optionals = new Set();
    data.forEach((item) => {
      if (typeof item !== 'object' || item === null) {
        types.add(js2dts(item, indent, path + '[]', options));
      } else if (!objType) {
        objType = new Map();
        Object.keys(item).forEach((key) => {
          objType.set(key, js2dts(item[key], indent + '  ', path + '[].' + key, options));
        });
      } else {
        const keys = Object.keys(item);
        if (keys.some((k) => objType.has(k))) {
          keys.forEach((k) => {
            if (!objType.has(k)) {
              optionals.add(k);
            }
            const type = js2dts(item[k], indent, path + '[].' + k, options);
            if (objType.get(k) === 'unknown[]' || !objType.has(k)) {
              objType.set(k, type);
            } else if (type !== 'unknown[]' && objType.get(k).indexOf(type) === -1) {
              objType.set(k, wrapFn(objType.get(k)) + ' | ' + wrapFn(type));
            }
          });
          Array.from(objType.keys()).forEach((k) => {
            if (!keys.includes(k)) {
              optionals.add(k);
            }
          });
        } else {
          types.add(js2dts(item, indent, path + '[]', options));
        }
      }
    });
    if (objType) {
      types.add(
        objType.size === 0
          ? '{}'
          : `{\n${Array.from(objType.entries())
              .map(([k, v]) => `${indent}  ${k}${optionals.has(k) ? '?' : ''}: ${v};`)
              .join('\n')}\n${indent}}`,
      );
    }
    if (types.size === 1) {
      return wrapFn(types.keys().next().value) + '[]';
    }
    return 'Array<' + Array.from(types).map(wrapFn).join(' | ') + '>';
  }
  if (options.isMap?.(path)) {
    const items = js2dts(Object.values(data), indent, path, options).replace(/^Array<|(?:>|\[])$/g, '');
    return `Record<string, ${items}>`;
  }
  const fields = Object.keys(data)
    .map((k) => {
      return '  ' + indent + JSON.stringify(k) + ': ' + js2dts(data[k], indent + '  ', path + '.' + k, options) + ';';
    })
    .join('\n');
  return fields ? `{\n${fields}\n${indent}}` : `{}`;
}

exports.js2dts = js2dts;

if (module === require.main) {
  if (process.argv.includes('--country-data-list')) {
    const d = require('country-data-list');
    d.countries = { all: d.countries.all };
    d.currencies = { all: d.currencies.all };
    d.languages = { all: d.languages.all };
    d.callingCountries = { all: d.callingCountries.all };
    d.callingCodes = { all: d.callingCodes.all };
    fs.outputFileSync(
      './src/types/country-data-list.d.ts',
      `/* This file is generated by ${JSON.stringify(process.argv.join(' '))}, please do not edit it. */

declare module 'country-data-list' {
  declare const countryDataList: ${js2dts(d, '  ', '', {
    isMap: (path) => path === '.continents' || path === '.regions',
  })};
  export = countryDataList;
}`,
    );
  } else {
    // prettier-ignore
    singleArrayCmd(true, {"code":200,"message":"Operation is successful !","timestamp":1606183233,"data":[{"id":"7425","zip_code":"20001","place_name":"Washington","state":"District of Columbia"},{"id":"7426","zip_code":"20002","place_name":"Washington","state":"District of Columbia"},{"id":"7427","zip_code":"20003","place_name":"Washington","state":"District of Columbia"},{"id":"7428","zip_code":"20004","place_name":"Washington","state":"District of Columbia"},{"id":"7429","zip_code":"20005","place_name":"Washington","state":"District of Columbia"},{"id":"7430","zip_code":"20006","place_name":"Washington","state":"District of Columbia"},{"id":"7431","zip_code":"20007","place_name":"Washington","state":"District of Columbia"},{"id":"7432","zip_code":"20008","place_name":"Washington","state":"District of Columbia"},{"id":"7433","zip_code":"20009","place_name":"Washington","state":"District of Columbia"},{"id":"7434","zip_code":"20010","place_name":"Washington","state":"District of Columbia"},{"id":"7435","zip_code":"20011","place_name":"Washington","state":"District of Columbia"},{"id":"7436","zip_code":"20012","place_name":"Washington","state":"District of Columbia"},{"id":"7437","zip_code":"20013","place_name":"Washington","state":"District of Columbia"},{"id":"7438","zip_code":"20015","place_name":"Washington","state":"District of Columbia"},{"id":"7439","zip_code":"20016","place_name":"Washington","state":"District of Columbia"},{"id":"7440","zip_code":"20017","place_name":"Washington","state":"District of Columbia"},{"id":"7441","zip_code":"20018","place_name":"Washington","state":"District of Columbia"},{"id":"7442","zip_code":"20019","place_name":"Washington","state":"District of Columbia"},{"id":"7443","zip_code":"20020","place_name":"Washington","state":"District of Columbia"},{"id":"7444","zip_code":"20022","place_name":"Washington","state":"District of Columbia"}]})
    // prettier-ignore
    singleArrayCmd(true, {"code":200,"message":"Operation is successful !","timestamp":1606103891595,"data":{"customerList":[{"clientColor":"#333333","lastName":"gmail","lastServiceTime":"2020-11-21","petList":[{"petId":15000039,"customerId":10000031,"petName":"DeeDee","breed":"Affenpinscher"},{"petId":15000043,"customerId":10000031,"petName":"Cattee","breed":"Abyssinian"}],"totalBookings":13,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-19","firstName":"atest","inactive":0,"phoneNumber":"6668","totalPay":154.70,"customerId":10000031,"tagIdList":[10030,10012]},{"clientColor":"#333333","lastName":"remidner","lastServiceTime":"2020-11-18","petList":[{"petId":15000047,"customerId":10000034,"petName":"DeeDee","breed":"American Bulldog"}],"totalBookings":5,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-16","firstName":"aunconfirmed 1","inactive":0,"phoneNumber":"**********","totalPay":4.00,"customerId":10000034,"tagIdList":[]},{"clientColor":"#333333","lastName":"qq","lastServiceTime":"2020-11-19","petList":[{"petId":15000048,"customerId":10000035,"petName":"Catee","breed":"Affenpinscher"}],"totalBookings":4,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-17","firstName":"aunconfirmed 2","inactive":0,"phoneNumber":"***********","totalPay":0.00,"customerId":10000035,"tagIdList":[]},{"clientColor":"#333333","lastName":"b","lastServiceTime":"2020-11-18","petList":[{"petId":15000024,"customerId":10000017,"petName":"meo","breed":"Abyssinian"}],"totalBookings":3,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-16","firstName":"Babette","inactive":0,"phoneNumber":"10009","totalPay":0.00,"customerId":10000017,"tagIdList":[]},{"clientColor":"#333333","lastName":"c","lastServiceTime":"2020-11-19","petList":[{"petId":15000025,"customerId":10000018,"petName":"taa","breed":"Affenpinscher"}],"totalBookings":10,"businessId":100000,"avatarPath":"","upcomingBooking":"2020-11-26","expectedServiceTime":"2020-12-17","firstName":"Caitlin","inactive":0,"phoneNumber":"10007","totalPay":357.00,"customerId":10000018,"tagIdList":[]},{"clientColor":"#333333","lastName":"d","lastServiceTime":"2020-11-22","petList":[{"petId":15000026,"customerId":10000019,"petName":"Daisy","breed":"Abyssinian"}],"totalBookings":22,"businessId":100000,"avatarPath":"","upcomingBooking":"2020-11-23","expectedServiceTime":"2020-12-20","firstName":"Dahila","inactive":0,"phoneNumber":"10010","totalPay":0.00,"customerId":10000019,"tagIdList":[]},{"clientColor":"#b8e986","lastName":"profile","lastServiceTime":"2020-11-21","petList":[{"petId":15000002,"customerId":10000000,"petName":"name1","breed":"Affenpinscher"}],"totalBookings":4,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-07","firstName":"demoCustomer","inactive":0,"phoneNumber":"10001","totalPay":309.06,"customerId":10000000,"tagIdList":[10000,10012]},{"clientColor":"#333333","lastName":"asadasd","lastServiceTime":"2020-11-22","petList":[{"petId":15000070,"customerId":10000053,"petName":"sfsafasfzd","breed":"Affenpinscher"},{"petId":15000071,"customerId":10000053,"petName":"fdfdf","breed":"Abyssinian"}],"totalBookings":13,"businessId":100000,"avatarPath":"","upcomingBooking":"2020-11-24","expectedServiceTime":"2020-12-20","firstName":"fff","inactive":0,"phoneNumber":"***********","totalPay":10.00,"customerId":10000053,"tagIdList":[]},{"clientColor":"#333333","lastName":"Jang","lastServiceTime":"2020-11-20","petList":[{"petId":15000029,"customerId":10000022,"petName":"Baskin","breed":"American Bulldog"},{"petId":15000030,"customerId":10000022,"petName":"Robbins","breed":"American Bobtail"}],"totalBookings":10,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-18","firstName":"Jeff","inactive":0,"phoneNumber":"**********","totalPay":362.93,"customerId":10000022,"tagIdList":[]},{"clientColor":"#333333","lastName":"mes","lastServiceTime":"2020-11-17","petList":[{"petId":15000040,"customerId":10000032,"petName":"Catee","breed":"Abyssinian"}],"totalBookings":2,"businessId":100000,"avatarPath":"","upcomingBooking":"","expectedServiceTime":"2020-12-15","firstName":"rebook","inactive":0,"phoneNumber":"8577566","totalPay":0.00,"customerId":10000032,"tagIdList":[10020,10012]}],"customerCount":14},"success":true})
    // prettier-ignore
    singleArrayCmd(true, {"code":200,"message":"Operation is successful !","timestamp":1605585928696,"data":{"id":10,"businessId":100000,"seq":null,"name":"Joking Young 2","firstName":"Joking","lastName":"Young","telephone":"","mobile":"","email":"","website":"","address":"123","createTime":1605585928,"updateTime":1605585928},"success":true})
    // prettier-ignore
    singleArrayCmd(true, {"code":200,"message":"Operation is successful !","timestamp":1605585478536,"data":[{"id":6,"businessId":100000,"seq":0,"name":"Joking Young","firstName":"Joking","lastName":"Young 1","telephone":"","mobile":"","email":"","website":"","address":"123","createTime":1605584965,"updateTime":1605584999}],"success":true})
  }
}
