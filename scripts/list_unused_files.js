/*
 * @since 2020-10-30 11:34:33
 * <AUTHOR> <<EMAIL>>
 */

const G = require('glob');
const deps = require('../temp/deps.json');

const files = G.sync('src/**/*.{ts,tsx}');

for (const file of files) {
  if (/\.(spec|bench)\.ts$/.test(file)) {
    continue;
  }
  const fixedName = file.endsWith('.d.ts') ? file.substr(0, file.length - '.d.ts'.length) : file;
  if (!deps.tree[fixedName] && !deps.tree[file]) {
    console.log(file);
  }
}
