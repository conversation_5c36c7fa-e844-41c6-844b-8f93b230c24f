/*
 * @since 2020-08-19 18:46:29
 * <AUTHOR> <<EMAIL>>
 */

const fs = require('fs-extra');
const path = require('path');
const { select } = require('@inquirer/prompts');
const cp = require('child_process');
const SVGO = require('svgo');
const { replaceAttrBySelector, USE_CURRENT_COLOR } = require('./svgo-plugins');

// const yargs = require('yargs');
// const argv = yargs.options({
//   basedir: {
//     type: 'string',
//     default: './src/assets/icon',
//     description: '要生成 .d.ts 的 icon 所在目录',
//   },
//   prefix: {
//     type: 'string',
//     default: 'Icon',
//     description: '生成的变量名称的前缀',
//   },
//   preview: {
//     type: 'boolean',
//     default: false,
//     description: '是否生成预览文件 preview.html',
//   },
//   open: {
//     type: 'boolean',
//     default: false,
//     description: '是否在浏览器中打开预览文件',
//   },
// }).argv;

const svgo = new SVGO({
  plugins: [{ removeDimensions: true }, { replaceAttrBySelector: replaceAttrBySelector(USE_CURRENT_COLOR) }],
});

const actionList = [
  {
    name: 'SVG',
    value: 'svg',
    basedir: './src/assets/svg',
    prefix: 'Svg',
    preview: true,
    open: true,
  },
  {
    name: 'Image',
    value: 'image',
    basedir: './src/assets/image',
    prefix: 'Image',
    preview: false,
    open: false,
  },
  {
    name: 'Icon',
    value: 'icon',
    basedir: './src/assets/icon',
    prefix: 'Icon',
    preview: false,
    open: false,
  },
  {
    name: 'Image:NPS',
    value: 'image:nps',
    basedir: './src/assets/image/nps',
    prefix: 'Image',
    preview: false,
    open: false,
  },
];

async function main() {
  const answer = await select({
    message: '请选择要生成的类型',
    choices: actionList.map((item) => ({ name: item.name, value: item.value })),
  });

  const argv = actionList.find((item) => item.value === answer);

  if (!argv) {
    return;
  }

  const files = await fs.readdir(argv.basedir);
  const isSvg = argv.basedir.indexOf('/svg') > -1 || argv.basedir.indexOf('\\svg') > -1;
  const items = [];
  await Promise.all(
    files
      .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
      .map(async (file, i) => {
        if (/\.(svg|png|jpg|mp3|xlsx|gif)$/.test(file)) {
          const name = argv.prefix + file.replace(/(?:^|-|_|\.|@)+(.)/g, (_, $1) => $1.toUpperCase());
          if (isSvg) {
            const content = await fs.readFile(path.join(argv.basedir, file), 'utf8');
            const svg = await svgo.optimize(content);
            items[i] = `<div class="icon"><div class="svg">${svg.data}</div><div>${name}</div></div>`;
          } else {
            items[i] = `<div class="icon"><img src="./${file}" alt="${file}">${name}</div>`;
          }
          await fs.writeFile(
            `${argv.basedir}/${file}.d.ts`,
            `declare const ${name}: string;\nexport default ${name};\n`,
          );
        }
      }),
  );
  argv.preview &&
    (await fs.writeFile(
      `${argv.basedir}/preview.html`,
      `<!doctype html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="initial-width=device-width">
<title>MoeGo Icons</title>
<style>
.content {
  max-width: 1050px;
  margin: 0 auto;
  text-align: center;
}

.icons.orange {
  color: orange;
}

.icons.dark {
  background: #e0e0e0;
}

.icon {
  display: inline-block;
  text-align: center;
  margin: 25px;
  width: 100px;
  word-break: break-all;
  vertical-align: top;
}

.icon img {
  display: block;
  width: 50px;
  height: 50px;
  margin: 0 auto 12px;
}
</style>
</head>
<body>
<div class="content">
<h1>MoeGo Icons</h1>
<label><input type="checkbox" onchange="setOrange(event)"> Orange</label>
<label><input type="checkbox" onchange="setDark(event)"> Dark</label>
<div class="icons">
${items.join('')}
</div>
</div>
<script>
function setDark(e) {
  document.querySelector('.icons').classList.toggle('dark', e.target.checked)
}
function setOrange(e) {
  document.querySelector('.icons').classList.toggle('orange', e.target.checked)
}
</script>
</body>
</html>
`,
    ));

  console.log('Done!');
  if (argv.open) {
    cp.execSync(`open ${argv.basedir}/preview.html`);
  }
}

main();
