/*
 * @since 2024-07-16 13:12:32
 * <AUTHOR> <<EMAIL>>
 */

async function dumpNPS() {
  const load = async (uri, data) => {
    const r = await fetch(uri, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify(data),
    });
    return r.json();
  };
  const results = [];
  for (let i = 1; i < 100; i++) {
    console.log('fetch values', i);
    const d = await load('/moego.admin.metadata.v1.MetadataAdminService/DescribeValues', {
      keyId: '85',
      pagination: { pageSize: 500, pageNum: i },
    });
    for (const v of d.values) {
      Object.assign(v, JSON.parse(v.value));
    }
    results.push(...d.values);
    if (!d.values.length) {
      break;
    }
  }
  for (let i = 0; i < results.length; i += 30) {
    console.log('fetch accounts', i, results.length);
    await Promise.all(
      results.slice(i, i + 30).map(async (v) => {
        const { companies } = await load('/moego.admin.company.v1.CompanyService/ListCompany', {
          companyId: v.companyId,
          pagination: { pageSize: 1, pageNum: 1 },
        });
        const { deprecatedStaffs } = await load('/moego.admin.business.v1.StaffService/DescribeStaffs', {
          accountId: v.ownerId,
          pagination: { pageSize: 100, pageNum: 1 },
        });
        const { accounts } = await load('/moego.admin.account.v1.AccountService/DescribeAccounts', {
          id: v.ownerId,
          pagination: { pageSize: 1, pageNum: 1 },
        });
        v.account = accounts[0];
        v.staff = deprecatedStaffs.find((s) => s.id + '' === v.staffId + '');
        v.company = companies[0];
        if (!v.account || !v.staff || !v.company) {
          console.warn('Bad', v);
        }
      }),
    );
  }
  const csv = [
    [
      'Email',
      'Name (ID)',
      'Join Time',
      'Company Name (ID)',
      'Company Country',
      'Company Level (Location/Van)',
      'Staff Role',
      'Score',
      'Feedback',
      'Submit Time',
      'Last Open Time',
      'Last Download Time',
      'Company Create Time',
      'Staff ID',
      'Staff Create Time',
      'ID',
    ],
  ];
  for (const r of results) {
    csv.push([
      r.account.email,
      `${r.account.firstName} ${r.account.lastName} (${r.account.id})`,
      r.account.createdAt,
      `${r.company?.name || ''} (${r.companyId})`,
      r.company?.country,
      `${r.company?.level || ''} (${r.company?.locationNum ?? 0}/${r.company?.vansNum ?? 0})`,
      ['Company Staff', 'Company Owner', 'Enterprise Owner', 'Enterprise Staff'][r.staff?.employeeCategory] ??
        r.staff?.employeeCategory,
      r.score,
      r.feedback,
      r.submitTime,
      r.openDays,
      r.download,
      r.company ? new Date(r.company.createTime * 1e3).toISOString() : '',
      r.staffId,
      r.staff ? new Date(r.staff.createTime * 1e3).toISOString() : '',
      r.id,
    ]);
  }
  const text = csv.map((r) => r.map((c) => '"' + ((c ?? '') + '').replaceAll('"', '""') + '"').join(',')).join('\n');
  const blob = new Blob([text]);
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.target = '_blank';
  a.href = url;
  a.download = 'NPS 2024 H1.csv';
  a.click();
}
