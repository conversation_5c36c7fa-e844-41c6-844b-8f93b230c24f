#!/usr/bin/env bash
# @since 2021-10-20 16:47:01
# <AUTHOR> <EMAIL>

set -euo pipefail

FINAL_FILE="$HOME/Downloads/dependencies.csv"
TEMP_FILE="$HOME/Downloads/dependencies_tmp.csv"
ROOT_DIR=/Users/<USER>/git

echo "" > "$TEMP_FILE"
echo "Name,Version,Licenses,Homepage" > "$FINAL_FILE"

MAVEN_DIR="/Users/<USER>/.m2/repository"

function specify_license() {
  case "$1" in
    "github.com/envoyproxy/protoc-gen-validate") echo "Apache-2.0";;
    "github.com/go-resty/resty/v2") echo "MIT";;
    "github.com/golang/protobuf") echo "BSD-3-Clause";;
    "github.com/gorilla/websocket") echo "BSD-2-Clause";;
    "github.com/grpc-ecosystem/go-grpc-middleware") echo "Apache-2.0";;
    "github.com/stretchr/testify") echo "MIT";;
    "go.uber.org/zap") echo "MIT";;
    "google.golang.org/grpc") echo "Apache-2.0";;
    "google.golang.org/protobuf") echo "BSD-3-Clause";;
    "gopkg.in/natefinch/lumberjack.v2") echo "MIT";;
    "@mapbox/polyline") echo "BSD-3-Clause";;
    "echarts") echo "Apache-2.0";;
    "jsonlint") echo "MIT";;
    "mockjs") echo "MIT";;
    "mockjs") echo "MIT";;
    "react-native-chart-kit") echo "MIT";;
    "react-native-maps") echo "MIT";;
    "vuedraggable") echo "MIT";;
  esac
}

function resolve_license() {
  local Group="$1"
  local Artifact="$2"
  local Version="$3"
  Pom="$MAVEN_DIR/$(echo "$Group" | sed 's/\./\//g')/$Artifact/$Version/$Artifact-$Version.pom"
  if ! cat "$Pom" | dasel -r xml -w json ".project.licenses.license" > /dev/null; then
    if ! cat "$Pom" | dasel -r xml -w json ".project.parent" > /dev/null; then
      echo ""
    else
      Group=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.groupId")
      Artifact=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.artifactId")
      Version=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.version")
      resolve_license "$Group" "$Artifact" "$Version"
    fi
  elif ! cat "$Pom" | dasel -r xml -w json '.project.licenses.license' | jq 'map(.name) | join(",")' > /dev/null; then
    cat "$Pom" | dasel -r xml -w json '.project.licenses.license.name'
  else
    cat "$Pom" | dasel -r xml -w json '.project.licenses.license' | jq 'map(.name) | join(",")'
  fi
}

function resolve_url() {
  local Group="$1"
  local Artifact="$2"
  local Version="$3"
  Pom="$MAVEN_DIR/$(echo "$Group" | sed 's/\./\//g')/$Artifact/$Version/$Artifact-$Version.pom"
  if ! cat "$Pom" | dasel -r xml -w json ".project.url" > /dev/null; then
    if ! cat "$Pom" | dasel -r xml -w json ".project.parent" > /dev/null; then
      echo ""
    else
      Group=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.groupId")
      Artifact=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.artifactId")
      Version=$(cat "$Pom" | dasel -r xml -w plain ".project.parent.version")
      resolve_url "$Group" "$Artifact" "$Version"
    fi
  else
    cat "$Pom" | dasel -r xml -w json ".project.url"
  fi
}

npm_projects=(
  MoeGo_Admin
  moego-api
  moego-business-web-v2
  moego-ci-scripts
  moego-mobile
  moego-online-booking-v2
  moego-www-v2
)

for PROJ in ${npm_projects[@]}; do
  cd "$ROOT_DIR/$PROJ"
  jq -r '.dependencies * .devDependencies | keys[]' package.json | while read -r Name; do
    Version=$(jq -r .version "node_modules/$Name/package.json")
    License=$(jq .license "node_modules/$Name/package.json")
    Homepage=$(jq .homepage "node_modules/$Name/package.json")
    if [[ "$Homepage" == "null" ]] || [[ "$Homepage" == "" ]]; then
      Homepage="https://www.npmjs.com/package/$Name/v/$Version"
    fi
    if [[ "$License" == "null" ]]; then
      License="$(specify_license "$Name")"
    fi
    echo "$Name,$Version,$License,$Homepage" >> "$TEMP_FILE"
  done
done

maven_projects=(
  moego-api
  moego-api-config-server
)

for PROJ in ${maven_projects[@]}; do
  cd "$ROOT_DIR/$PROJ"
  mvn dependency:tree | grep -E '(] \\|] \+)' | grep -v moego | while read -r line; do
    Group=$(echo "$line" | awk -F"[ :]" '{print $3}')
    Artifact=$(echo "$line" | awk -F"[ :]" '{print $4}')
    Name="$Group:$Artifact"
    Version=$(echo "$line" | awk -F"[ :]" '{print $6}')
    Licenses="$(resolve_license "$Group" "$Artifact" "$Version")"
    Homepage="$(resolve_url "$Group" "$Artifact" "$Version")"
    if [[ "$Homepage" == "" ]]; then
      Homepage="https://mvnrepository.com/artifact/$Group/$Artifact/$Version"
    fi
    echo "$Name,$Version,$Licenses,$Homepage" >> "$TEMP_FILE"
  done
done

go_projects=(
  moego-ws
)

for PROJ in ${go_projects[@]}; do
  cd "$ROOT_DIR/$PROJ"
  grep -E '^\t' go.mod | grep -v '// indirect' | while read -r line; do
    Name=$(echo "$line" | awk '{print $1}')
    Version=$(echo "$line" | awk '{print $2}')
    Homepage="https://$Name"
    License=$(specify_license "$Name")
    echo "$Name,$Version,$License,$Homepage" >> "$TEMP_FILE"
  done
done

cat "$TEMP_FILE" | sort | uniq >> "$FINAL_FILE"
rm -rf "$TEMP_FILE"
