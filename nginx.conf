server {
  listen 9620;
  server_name _;
  gzip on;
  gzip_types *;

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri /index.html;

    add_header X-Frame-Options SAMEORIGIN;
  }

  location ~* \.map$ {
    deny all;
  }

  location /log/sheet {
    limit_except POST {
      deny all;
    }

    resolver $NAMESERVER valid=5s;

    set $proxy_host "moego-bff";
    if ($host ~ "^(.*)-grey-go") {
      set $proxy_host "moego-bff-feature-$1";
    }

    proxy_cache off;
    proxy_pass http://$proxy_host.$NAMESPACE.svc.cluster.local:8080/moego.bff/intercom/sheet;
    proxy_pass_request_body on;
    proxy_pass_request_headers on;

    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    proxy_http_version 1.1;
  }

  location /api/v2/rum-dd {
    limit_except POST {
      deny all;
    }

    resolver ******* valid=300s;

    proxy_cache off;
    proxy_pass https://browser-intake-us5-datadoghq.com/api/v2/rum$is_args$args;
    proxy_pass_request_body on;
    proxy_pass_request_headers on;

    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Connection ""; # Clear Connection header for keepalive to backend
    proxy_set_header Cookie ""; # Remove Cookie header for security reasons

    proxy_http_version 1.1;
  }

  location /api/v2/logs-dd {
    limit_except POST {
      deny all;
    }

    resolver ******* valid=300s;

    proxy_cache off;
    proxy_pass https://browser-intake-us5-datadoghq.com/api/v2/logs$is_args$args;
    proxy_pass_request_body on;
    proxy_pass_request_headers on;

    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Connection ""; # Clear Connection header for keepalive to backend
    proxy_set_header Cookie ""; # Remove Cookie header for security reasons

    proxy_http_version 1.1;
  }
}
