# Guide

**TOC**

-   [状态管理](#状态管理)
-   [组件样式](#组件样式)
-   [Icon](#Icon)
-   [路由跳转](#路由跳转)
-   [时间戳](#时间戳)
-   [代码规范](#代码规范)
-   [工具](#工具)
-   [构建与发布](#构建与发布)

## 状态管理

状态管理采用 [Redux](https://redux.js.org/) + [React-Redux](https://react-redux.js.org/) + [Immutable.js](https://immutable-js.github.io/immutable-js/).

参考: [common](./src/store/common).

一个 `reducer` 占用一个 `namespace`, 新建一个 reducer 时, 首先创建以下几个文件:
`src/store/<namespace>/<namespace>_{types,actions,reducer,selectors}.ts`

其中 `types.ts` 定义 `action` 类型, 提供了工具函数 `createActionTypes` 来初始化:

```typescript jsx
createActionTypes(
    {
        switches: {
            toggle: '',
        },
    },
    '@moego/common',
);
```

`reducer.ts` 定义 reducer 及 state 数据结构, 定义 reducer 提供了工具函数 `createReducer` 来初始化, 为了简化
reducer 中对 immutable 数据的操作, 提供了几个工具类 `OwnList`, `OwnMap`, `PagedList`, `RecordMap`:

1. 定义一个 immutable record

    ```typescript jsx
    export class SwitchRecord extends Record({
        field: '',
        opened: false,
        switching: false,
    }) {}
    ```

2. 定义一个 immutable state record

    ```typescript jsx
    export class CommonStateRecord extends Record({
        switches: new RecordMap(SwitchRecord, 'field'),
    }) {}
    ```

3. 定义 reducer

    ```typescript jsx
    export const commonReducer = createReducer('common', new CommonStateRecord(), {
        [commonActions.switches.toggle]: (state, action: ToggleSwitchAction) => {
            return state.merge({
                switches: state.switches.mergeItem(action.field, action),
            });
        },
    });
    ```

4. 注册 reducer: 目前集中注册, 在 [store/index.ts](./src/store/index.tsx) 中按需修改 `rootReducer` 的
   初始化方式即可.

`actions.ts` 定义 action creator, 提供了工具函数 `createAction` 来初始化:

```typescript jsx
export type ToggleSwitchAction = RecordProps<SwitchRecord>;

export const toggleSwitch = action(async (dispatch, select, field: string, opened: boolean, switching: boolean) => {
        field,
        opened,
        switching,
    }),
);
```

`selectors.ts` 提供外部调用的 api, 理论上, selectors 应该提供完整的对 state 进行访问的 api, 相当于外部组件无需关注
reducer 中 state 的结构, 只需要关注 selector 提供的 api 即可. 提供了工具函数 `createRecordSelector`, `createOwnedEntitiesSelector`,
`currySelector` 等工具函数来创建 `selector`:

```typescript jsx
// 根据 key 获取一个 switchRecord
export const selectSwitch = createRecordSelector(as<CommonState>(), 'common', 'switches');

// 此 selector 在组件中可以被如下形式调用:
// 1. 返回一个指定 key 的 selector:
const [isOpen] = useSelector(selectSwitch('UI/LEFT_NAV'));
// 2. 传入 state 及 key 获取 switchRecord:
const [isOpen2] = useSelector((state: CommonState) => selectSwitch(state, 'UI/LEFT_NAV'));
// 3. 以上两种方式, 均允许传入一个附加参数, 如果为 true, 则在指定 key 不存在时返回 Record 的默认值,
// 在这里为 SwithcRecord { field: '', opened: false, switching: false }
const [isOpen3, isOpen4] = useSelector(selectSwitch('UI/NOT_EXIST', true), selectSwitch('UI/NOT_EXIST'));

console.log(isOpen === isOpen2); // => true
console.log(isOpen.opened); // => true
console.log(isOpen3, isOpen4); // => undefined, { field: '', opened: false, switching: false }
```

在组件中, 使用 `useSelector`, `useShallowSelector`, `useSelector` 来 select 状态, 期中:

-   `useSelector` 为 `react-redux` 的基础函数, 其接受一个 selector, 同时允许指定比较函数来判断是否需要重新渲染, 默认为严格等于.
-   `useShallowSelector` 为对 `useSelector` 的封装, 提供 `shallowEqual` 作为比较函数.
-   `useSelector` 是一个工具函数, 其允许传入多个 selector, 返回一个数组, 对于每个 selector, 使用严格等于来检查是否需要重新渲染.

    ```typescript jsx
    const [isOpen, business] = useSelector(selectSwitch('UI/LEFT_NAV'), selectBusiness);
    ```

## 组件样式

组件样式采用 [styled-components](https://styled-components.com/) 方案, 其优势在于: 完善的 css in js 表达, 全局样式管理,
prettier 默认支持等.

其使用方式请参考官方文档.

## Icon

现在设计师给的切图, 基本上都是 svg, 用 `url-loader` 去加载 svg, 不能更换颜色, 因此建议所有的 svg 都使用 `raw-loader` 加载.
为了兼容已有 icon, 目前限制只有在 `/svg/` 目录下的 `.svg` 文件才会被 `raw-loader` 加载, 此方式加载进来的 icon 可以用 `SvgIcon`
进行渲染. [比如](./src/container/LeftNav/LeftNav.tsx#L66):

```typescript jsx
import { SvgIcon } from '../../components/Icon/Icon';
import SvgMenuMoreSvg from '../../assets/svg/menu-more.svg';

<SvgIcon src={SvgMenuMoreSvg} size={28} onClick={() => dispatch(toggleSwitch(KEY_OPEN, !open, false))} />;
```

## 路由跳转

页面之间跳转是页面级别的依赖, 页面跳转可能会涉及三类参数: `params`, `search`, `state`, 目前我们三种都有用到

-   `params` 用得最多, 比如跳转到 client overview `/client/:customerId/overview`
-   `search` 用于 `params` 无法表达的情况下, 并且需要允许用户复制链接完整复现 location 状态时使用, 如登录页的 `inviteCode`, `redirectUrl`
-   `state` 用于无需被用户控制的状态, 如 `createTicket` 页面

为了便于跳转, 提供了工具类 [RoutePath](./src/utils/RoutePath.ts), 同时约定所有的页面的路径均定义于
[paths.ts](./src/router/paths.ts) 中(暂时, 后面 container 模块化后再拆分), 如:

```typescript jsx
export const customerOverview = new RoutePath<CustomerParams>('/client/:customerId/overview');
```

`RoutePath` 提供了三个方法: `build`, `stated`, `queried` 用来创建一个跳转路径, 其参数请查看文件中的文档.

## 时间戳

1.  前后端时间交互的地方:

    1. 日期(如生日, 预约日期等), 时间戳(如创建时间, 修改时间等), 均采用以秒为单位的时间戳格式
    2. 时间点(如预约的时间范围, 员工的工作时间范围), 均采用以分钟为单位的时间戳格式

2.  时间戳存在两种:

    1. 一种是标准时间戳, 其在前端展示时会根据 business 设置的 timezoneSeconds 来设置时区进行展示, 例如: 一个时间戳的值为 `0`, business
       设置的时区为 `Asia/Shanghai`, 对应的 `timezoneSeconds` 为 `+28800`, 前端将展示为 `1970-01-01 08:00:00`.
    2. 另一种是固定时间戳, 我们称之为 `Fixed`, 其展示不会因为 business 的 timezone 设置而变化, 比如生日, 员工的工作时间, 预约的日期,
       时间等. 例如: 如果一个预约的日期被设置成 `1970-01-01`, 时间被设置成 `08:00`, 无论 business 的 timezone 是 `+08:00`
       还是 `-05:00`, 前端都会认为日期是 `1970-01-01`, 时间是 `08:00`, 展示时也应该是此值.

3.  前端上传时间戳:

    根据 `2` 中的描述, 不同场景下上传的时间戳值不同

    1. 对于第一种, 前端直接通过 `Math.round(date.valueOf() / 1e3)` 获取时间戳即可, 其中 `date` 是一个 `Date` 或 `Dayjs` 对象, 无需
       关注时区.
    2. 对于第二种, 情况会比较复杂, 如果只有时间部分, 即 `1.1` 中描述的情况, 其值获取为 `hour * 60 + minute`, 如果包含日期, 则需要纠正
       时区偏移后上传: `Math.round(date.valueOf() - date.getTimezoneOffset() * 60000)`, 这里的 `date` 是标准的 `Date` 对象,
       如果是 `Dayjs`, 则计算方式为 `Math.round(date.valueOf() + date.utcOffset())`.

4.  后端下发时间戳:

    1. 如果一个时间戳来在客户端, 则应按照原样下发
    2. 如果由服务端生成, 则生成的时间应按照 `2` 中的描述生成
    3. 时间戳的默认值为 `0`, 不应该受服务器所在时区影响

5.  前端时间展示

    前端的时区, 日期格式, 时间格式均由 business preference 设定决定, 因此, `BusinessRecord` 提供了相应的函数来格式化日期/时间.
    _下面的代码示例中, 认为 business 的: 时区为 `+08:00`, 即中国, 日期格式为 `YYYY-MM-DD`, 时间格式为 `12 小时制`_

    1. 组件中获取 business 对象

        ```typescript jsx
        import { selectBusiness } from './business.selectors';
        import { useSelector } from 'react-redux';

        export const AwesomeComponent = () => {
            const business = useSelector(selectBusiness);
            return business.formatTime(0); // => '00:00 am'
        };
        ```

    2. 格式化时间

        ```typescript jsx
        business.formatTime(0); // => 08:00 am
        business.formatTime(new Date(0)); // => 08:00 am
        ```

    3. 格式化固定(Fixed)时间

        ```typescript jsx
        business.formatFixedTime(0); // => 00:00 am
        ```

    4. 格式化日期

        ```typescript jsx
        business.formatDate(0); // => 1970-01-01
        ```

    5. 格式化固定(Fixed)日期

        ```typescript jsx
        business.formatFixedTime(0); // => 1970-01-01
        ```

    6. 格式化日期时间

        ```typescript jsx
        business.formatDateTime(0); // => 1970-01-01 08:00 am
        ```

    7. 格式化固定(Fixed)日期时间

        ```typescript jsx
        business.formatFixedTime(0); // => 1970-01-01 00:00 am
        ```

6.  前端表单提交时间 _WIP_

    **注意: 此接口尚未最终确定, 可能会慢慢迭代演进!**

    `utils` 中提供了一个 [ModelConvertor](./src/utils/form.ts), 其作用是提供了一个 `state` -> `store` -> `input` 自动转换
    的工具, 其中 `state` 是指前端自己保存的状态, `store` 是指 `form` 表单中储存的状态, `input` 是指服务端接口所需要的数据, 其主要
    提供了三类接口:

    1.  配置转换关系:

        1.  `convertor.copy(...args: string[])`: 表示由 args 指定的字段将直接由 `state` 复制到 `store` 再复制到 `input`, 不
            做任何转换操作
        2.  `convertor.bool(...args: string[])`: 表示由 args 指定的字段类型转换关系为: `(0, 1)` -> `(false, true)` -> `(0, 1)`
        3.  `convertor.date(...args: string[])`: 表示由 args 指定的字段类型转换关系为: `unix seconds` -> `Dayjs` -> `unix seconds`
        4.  `convertor.field(fieldName, stateType, storeType, inputType)`: 自定义转换关系, 其中起决定性作用的是 `storeType`,
            其类型可以是 `copy`, `bool`, `date`, `utc`. `stateType` 和 `inputType` 有相同的含义, 允许的转换关系及含义如下:

                <table>
                    <tr><th>storeType</th><th>storeType 描述</th><th>stateType/inputType</th><th>stateType/inputType 描述</th></tr>
                    <tr><td rowspan="1">copy</td><td>直接复制</td><td>copy</td><td>直接复制</td></tr>
                    <tr><td rowspan="2">bool</td><td rowspan="2">true/false</td><td>bool</td><td>强制转换</td></tr>
                    <tr><td>num</td><td>1/0</td></tr>
                    <tr><td rowspan="3">date/utc</td><td rowspan="3">根据 storeType 转换成 Dayjs, 如果是 date, 会使用浏览器时区, 如果是 utc, 则时区为 +00:00</td><td>date</td><td>日期字符串 <code>YYYY-MM-DD</code></td></tr>
                    <tr><td>unix</td><td>时间戳, 单位为秒</td></tr>
                    <tr><td>ms</td><td>时间戳, 单位为毫秒</td></tr>
                </table>

    2.  将 `state` 转换到 `store`

        1. `convertor.attach(form, model)`: 如果 `state` 为空, 将清除 `form` 中的所有项, 否则会将 `model` 转换后再 `setFieldsValue`

    3.  从 `store` 中获取 `input`

        1. `convertor.validate(form)`: 验证 `form`, 并将验证结果转换后输出

    Example:

    1. [VaccineBindingModal](./src/container/Client/ClientInfo/Pets/components/VaccineBindingCard.tsx#L167)
    2. [PetCommonModal](./src/container/settings/PetSetting/components/PetCommonModal.tsx#L29)

## 代码规范

对代码格式的一些约束:

1. 禁止使用 `export default <Expression>`, 应该使用 `export <Declaration>`, 或者 `export <NamedExports>`

    ```typescript jsx
    // good
    export function greet(name: string) {
        return `hello ${name}!`;
    }

    // good
    export { greet };
    export { greet as myGreet };
    ```

    ```typescript jsx
    // bad
    export default (name: string) => `hello ${name}!`;

    // bad
    const Foo = () => <div>Foo</div>;
    export default Foo;
    ```

2. 禁止使用 `index.{ts,tsx}` 来命名文件, 文件名应该有具体的语义
3. 禁止使用 path alias, 无论是 typescript 还是 webpack, monorepo 项目除外

## 工具

1. 本地构建并推送到测试服务器

    ```bash
    ./scripts/build_test.sh
    ```

2. 生成 icon 的 .d.ts 文件以便于 import

    ```bash
    node ./scripts/build_icons.js --basedir ./src/assets/svg --prefix Svg --preview --svg --open

    # 参数说明:
    Options:
     --help     Show help                                                 [boolean]
     --version  Show version number                                       [boolean]
     --basedir  要生成 .d.ts 的 icon 所在目录
                                            [string] [default: "./src/assets/icon"]
     --prefix   生成的变量名称的前缀                         [string] [default: "Icon"]
     --preview  是否生成预览文件 preview.html               [boolean] [default: false]
     --open     是否在浏览器中打开预览文件                    [boolean] [default: false]
    ```

3. 创建标准的新文件

    ```bash
    ./scripts/new_file.sh ./src/components/HelloWorld/HelloWorld.{style.ts,tsx}
    ```

4. 同步 `moego-api` 中的错误码到本地

    ```bash
    node ./scripts/build_error_codes.js
    ```

## 构建与发布

目前处于过渡阶段, 依旧在主干上开发, 仅讨论当前阶段的机制.

目前阶段会存在三种 target

-   本地开发: pnpm start
-   测试环境: 需要包括一些未开发的功能入口, 比如 stripe
-   生产环境: beta 1 的功能正常

基于上述情况, 构建时会分别设置 NODE_ENV 为 `development`, `test`, `production`.
